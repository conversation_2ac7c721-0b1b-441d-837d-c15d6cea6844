[project]
name = "audio"
version = "0.1.0"
description = "A MCP server project"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "boto3>=1.38.7",
    "elevenlabs>=1.57.0",
    "mcp==1.9.0",
    "openai (>=1.65.5,<2.0.0)",
    "pyht>=0.1.12",
    "requests>=2.32.3",
    "werkzeug>=3.1.3",
]
[[project.authors]]
name = "AkashAaglawe22012"
email = "<EMAIL>"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
audio = "audio:main"
