import logging
import os
import uuid
from ..helper.utils import split_long_string, create_folder_if_not_exists, remove_file
from ..helper.s3_manager import S3Uploader
from elevenlabs.client import ElevenLabs
from elevenlabs import VoiceSettings

# Configure logging
logging.basicConfig(
    format=(
        "%(asctime)s - %(name)s - %(levelname)s "
        "[%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s"
    ),
    level=logging.ERROR,
)
logger = logging.getLogger(__name__)


class ElevenLabsService:
    def __init__(self):
        api_key = os.getenv("ELEVENLABS_API_KEY")
        if not api_key:
            raise ValueError("ELEVENLABS_API_KEY environment variable not set")
        self.client = ElevenLabs(api_key=api_key)
        # You can set a default voice/model here if desired
        self.default_model_id = "eleven_multilingual_v2"
        self.default_output_format = "mp3_44100_128"
        self.default_voice_id = "JBFqnCBsd6RMkjVDRZzb"  # Change as needed
        self.s3_uploader = S3Uploader()

    def create_voice(self, text, voice_id=None):
        """
        Generate speech audio from text using ElevenLabs API.
        Returns the path to the saved audio file.
        """
        if not voice_id:
            voice_id = self.default_voice_id

        # You can customize voice settings as needed
        voice_settings = VoiceSettings(
            stability=0.5,
            similarity_boost=0.75,
            style=0.0,
            use_speaker_boost=True,
            speed=1.0,
        )

        try:
            response = self.client.text_to_speech.convert(
                text=text,
                voice_id=voice_id,
                model_id=self.default_model_id,
                output_format=self.default_output_format,
                voice_settings=voice_settings,
            )
            audio_id = str(uuid.uuid4())

            create_folder_if_not_exists("./temp")

            save_file_path = f"./temp/{audio_id}.mp3"
            with open(save_file_path, "wb") as f:
                for chunk in response:
                    if chunk:
                        f.write(chunk)
            logger.info(f"Audio file saved: {save_file_path}")
            return audio_id, save_file_path
        except Exception as e:
            logger.error(f"Error generating voice: {str(e)}")
            raise e

    def get_voice(self, audio_path):
        """
        For compatibility: returns the audio file path.
        """
        if os.path.exists(audio_path):
            return audio_path
        else:
            logger.error(f"Audio file not found: {audio_path}")
            raise FileNotFoundError(f"Audio file not found: {audio_path}")

    def create_voices(self, text, voice_id=None):
        """
        Split long text and generate multiple audio files.
        Returns a list of audio file paths.
        """
        scripts = split_long_string(text)
        audio_paths = []
        audio_ids = []
        for index, script in enumerate(scripts):
            try:
                audio_id, audio_path = self.create_voice(script, voice_id)
                audio_paths.append(audio_path)
                audio_ids.append(audio_id)
            except Exception as e:
                logger.error(f"Error creating voice for script {index}: {str(e)}")
                raise
        return audio_ids, audio_paths

    async def get_voice_audios(self, audio_ids):
        """
        For compatibility: returns the list of audio file paths.
        """
        audio_links = []
        for audio_id in audio_ids:
            try:
                audio_path = f"./temp/{str(audio_id)}.mp3"
                if os.path.exists(audio_path):
                    url = self.s3_uploader.upload_file(
                        "audio",
                        audio_path,
                        new_file_name=f"{uuid.uuid4()}.mp3",
                    )
                    audio_links.append(url)
                    remove_file(audio_path)
                    logger.info(f"Audio file exists: {audio_path}")
                else:
                    logger.error(f"Audio file not found: {audio_path}")
            except Exception as e:
                logger.error(
                    f"Error getting voice for audio_path {audio_path}: {str(e)}"
                )
                raise e
        return audio_links

    def get_list_of_voices(self):
        """
        Fetches the list of available voices from ElevenLabs.
        Returns a list of voice dicts.
        """
        try:
            voices = self.client.voices.get_all()
            return voices
        except Exception as e:
            logger.error(f"Error fetching voices: {str(e)}")
            raise
