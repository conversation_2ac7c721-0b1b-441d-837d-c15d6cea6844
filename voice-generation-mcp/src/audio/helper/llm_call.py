import re
import json
import openai

# import google.generativeai as genai
# import anthropic
from openai import OpenAI

from ..constants.enum import Model<PERSON>rov<PERSON>
from typing import Tuple, List
from .model_config import (
    default_openai_model,
)
from .config import OPENAI_API_KEY


from .prompts import build_script_text_prompt


def generate_response(
    prompt: str,
    provider=ModelProvider.OPENAI,
) -> str:
    """
    Generate a script for a video, depending on the subject of the video.
    Args:
        video_subject (str): The subject of the video.
    Returns:
        str: The response from the AI model.
    """
    try:
        if provider == ModelProvider.OPENAI:
            openai.api_key = OPENAI_API_KEY
            response = openai.chat.completions.create(
                model=default_openai_model,
                messages=[{"role": "user", "content": prompt}],
            )
            token_used = response.usage.total_tokens
            print("openai token_used ===>>", token_used)
            response = response.choices[0].message.content
        # if provider == ModelProvider.GOOGLE:
        #     genai.configure(api_key=GEMINI_API_KEY)
        #     model = genai.GenerativeModel(default_gemini_model)
        #     response_model = model.generate_content(prompt)
        #     print(
        #         "google token_used ===>>",
        #         response_model.usage_metadata.total_token_count,
        #     )
        #     response = response_model.text
        # if provider == ModelProvider.DEEPSEEK:
        #     # for backward compatibility, you can still use `https://api.deepseek.com/v1` as `base_url`.
        #     client = OpenAI(api_key=DEEPSEEK_API, base_url="https://api.deepseek.com")
        #     response = client.chat.completions.create(
        #         model=default_deepseek_model,
        #         messages=[
        #             {"role": "system", "content": "You are a helpful assistant"},
        #             {"role": "user", "content": prompt},
        #         ],
        #         max_tokens=1024,
        #         temperature=0.7,
        #         stream=False,
        #     )
        #     response = response.choices[0].message.content
        # if provider == ModelProvider.ANTHROPIC:
        #     client = anthropic.Anthropic(
        #         api_key=CLAUDE_KEY,
        #     )
        #     message = client.messages.create(
        #         model=default_claude_model,
        #         max_tokens=1024,
        #         messages=[{"role": "user", "content": prompt}],
        #     )
        #     response = message.content
        return response
    except Exception as e:
        print(f"[-] Error: openai generate_response {str(e)}")
        raise


def get_script_text(script):
    prompt = build_script_text_prompt(script)
    return generate_response(prompt).strip()
