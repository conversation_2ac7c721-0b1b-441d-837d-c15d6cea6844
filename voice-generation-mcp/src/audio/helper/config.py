import os
import logging
from dotenv import load_dotenv

# Load environment variables from a .env file
load_dotenv()


# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)

# Access environment variables and store them in variables
DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")

HOST = os.getenv("HOST", "localhost")
PORT = int(os.getenv("PORT", 5004))


PLAY_API_KEY = os.getenv("PLAY_API_KEY")
PLAY_USER_ID = os.getenv("PLAY_USER_ID")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")

AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
S3_BUCKET_NAME = os.getenv("S3_BUCKET_NAME")
AWS_BASE_URL = os.getenv("AWS_BASE_URL")
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")
