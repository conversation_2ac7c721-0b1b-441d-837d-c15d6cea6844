import requests
import mimetypes
import os


def split_long_string(input_string):
    if len(input_string) <= 200:
        return [input_string]
    else:
        substrings = []
        start = 0
        end = 200
        while start < len(input_string):
            substrings.append(input_string[start:end])
            start = end
            end += 200
            if end > len(input_string):
                end = len(input_string)
        return substrings


def get_mime_type(url):
    # Try to get MIME type from file extension
    mime_type, _ = mimetypes.guess_type(url)
    if mime_type:
        return mime_type
    # Fallback: Fetch headers for Content-Type
    try:
        response = requests.head(url, allow_redirects=True)
        content_type = response.headers.get("Content-Type")
        if content_type:
            return content_type.split(";")[0]  # Remove encoding info if present
    except requests.RequestException:
        pass
    return "unknown/unknown"  # Default if no MIME type is found


def create_folder_if_not_exists(folder_path):
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)


def remove_file(file_path):
    if os.path.exists(file_path) and os.path.isfile(file_path):
        os.remove(file_path)
