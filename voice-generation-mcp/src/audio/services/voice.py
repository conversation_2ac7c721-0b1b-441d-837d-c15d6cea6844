# from pydantic import AnyUrl
# import mcp.server.stdio
import logging
import requests
import time
from ..helper.config import PLAY_API_KEY, PLAY_USER_ID


# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s",
    level=logging.ERROR,
)
logger = logging.getLogger(__name__)


def create_voice(text, voice_id):
    url = "https://api.play.ht/api/v2/tts"
    payload = {
        "text": text,
        "voice": voice_id,
        "output_format": "mp3",
        "voice_engine": "PlayHT2.0",
        "quality": "high",
    }
    headers = {
        "accept": "application/json",
        "content-type": "application/json",
        "AUTHORIZATION": PLAY_API_KEY,  # authorization
        "X-USER-ID": PLAY_USER_ID,
    }
    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
        logger.info(f"Voice creation response: {response.text}")
        return response.json()["id"]
    except requests.exceptions.RequestException as e:
        logger.error(f"Error in create_voice: {str(e)}")
        raise


def split_long_string(input_string):
    if len(input_string) <= 2000:
        return [input_string]
    else:
        substrings = []
        start = 0
        end = 2000
        while start < len(input_string):
            substrings.append(input_string[start:end])
            start = end
            end += 2000
            if end > len(input_string):
                end = len(input_string)
        return substrings


def get_voice_audios(audio_ids):
    audio_links = []
    for audio_id in audio_ids:
        try:
            voice_link = get_voice(audio_id)
            audio_links.append(voice_link)
        except Exception as e:
            logger.error(f"Error getting voice for audio_id {audio_id}: {str(e)}")
    return audio_links


def get_voice(audio_id):

    request_url = f"https://api.play.ht/api/v2/tts/{audio_id}"
    headers = {
        "accept": "application/json",
        "AUTHORIZATION": authorization,
        "X-USER-ID": USER_ID,
    }
    max_retries = 10
    retry_count = 0
    while retry_count < max_retries:
        try:
            response = requests.get(url=request_url, headers=headers)
            response.raise_for_status()
            data = response.json()
            if data["status"] == "failed":
                raise ("Failed to generate voice")
            if data["status"] == "complete":
                return data["output"]["url"]
            logger.info("Waiting for voice generation...")
            time.sleep(30)
            retry_count += 1
        except requests.exceptions.RequestException as e:
            logger.error(f"Error in get_voice: {str(e)}")
            raise
    raise TimeoutError("Voice generation timed out")
