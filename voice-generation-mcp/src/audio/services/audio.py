from ..providers.playht import PlayHtService
from ..providers.elevenlabs import ElevenLabsService
from ..constants.enum import VoiceProvider
from werkzeug.exceptions import InternalServerError

from ..helper.llm_call import get_script_text
from ..helper.utils import get_mime_type


play_ht_service = PlayHtService()
elevenlabs_service = ElevenLabsService()


async def generate_audio(script, voice_id, provider=VoiceProvider.ELEVENLABS.value):
    try:
        script = get_script_text(script)

        print(f"script: {script}")

        if provider == VoiceProvider.PLAYHT.value:
            audio_ids = play_ht_service.create_voices(script, voice_id)
        elif provider == VoiceProvider.ELEVENLABS.value:
            audio_ids, audio_paths = elevenlabs_service.create_voices(script, voice_id)
        else:
            raise ValueError("Invalid provider specified")

        return {
            "audio_ids": audio_ids,
            "voice_id": voice_id,
            # "audio_script": script,
        }
    except Exception as err:
        print(f"[-] Error: generate_audio {str(err)}")
        raise InternalServerError("Audio generation failed")


async def get_updated_audio(audio_ids, provider=VoiceProvider.ELEVENLABS.value):
    try:

        if provider == VoiceProvider.PLAYHT.value:
            audio_links = await play_ht_service.get_voice_audios(audio_ids)
        elif provider == VoiceProvider.ELEVENLABS.value:
            audio_links = await elevenlabs_service.get_voice_audios(audio_ids)
        else:
            raise ValueError("Invalid provider specified")

        return {
            "audio_urls": audio_links,
            "mimetype": get_mime_type(audio_links[0]),
        }
    except Exception as err:
        raise err
