from pydantic import BaseModel, Field
from .enum import VoiceProvider


class GenerateAudio(BaseModel):
    script: str = Field(
        ..., min_length=1, max_length=10000, description="Script is required"
    )
    voice_id: str = Field(..., min_length=1, max_length=50)
    provider: VoiceProvider = Field(
        default=VoiceProvider.ELEVENLABS.value, description="Optional voice provider platform"
    )


class FetchGenerateAudio(BaseModel):
    audio_ids: list[str] = Field(
        ..., min_items=1, description="List of voice IDs is required"
    )
    provider: VoiceProvider = Field(
        default=VoiceProvider.ELEVENLABS.value, description="Optional voice provider platform"
    )
