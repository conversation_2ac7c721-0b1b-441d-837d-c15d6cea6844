[project]
name = "youtube-mcp"
version = "0.1.0"
description = "A MCP server project"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "api-client>=1.3.1",
    "apiclient>=1.0.4",
    "google-api-python-client>=2.166.0",
    "mcp>=1.6.0",
    "pytube>=15.0.0",
    "termcolor>=3.0.1",
    "werkzeug>=3.1.3",
    "youtube-transcript-api>=1.0.3",
    "yt-dlp>=2025.3.31",
]
[[project.authors]]
name = "akashaaglawe"
email = "<EMAIL>"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
youtube-mcp = "youtube_mcp:main"
