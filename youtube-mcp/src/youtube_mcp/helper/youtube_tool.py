import re
from werkzeug.exceptions import BadRequest, NotFound
from urllib.parse import urlparse, parse_qs
import logging
import requests
from termcolor import colored
import random
import json
import time
import io
import os
import yt_dlp
import requests

# from config import WEBSHARE_PROXY_API
from datetime import datetime, timedelta

from youtube_transcript_api import YouTubeTranscriptApi
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import re
import random
from pytube import YouTube

from ..constant.config import GOOGLE_CONSOLE_API_KEY, WEBSHARE_PROXY_API

logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s",
    level=logging.ERROR,
)
logger = logging.getLogger(__name__)

rotational_proxy = "http://pbldvjdg-rotate:<EMAIL>:80/"


def proxy_list():
    file_path = "./content/proxy.json"

    try:
        with open(file_path, "r") as file:
            data = json.load(file)
        if (
            datetime.fromisoformat(data.get("time")) + timedelta(seconds=2)
            < datetime.utcnow()
        ):
            # Select a random number between 1 and 4
            page = random.choice([1, 2, 3, 4, 5])
            response = requests.get(
                f"https://proxy.webshare.io/api/v2/proxy/list/?mode=direct&page={page}&page_size=20",
                headers={"Authorization": WEBSHARE_PROXY_API},
            )
            response.raise_for_status()
            proxies = response.json()["results"]
            proxies_list = [rotational_proxy]
            for proxy in proxies:
                url = f"http://{proxy['username']}:{proxy['password']}@{proxy['proxy_address']}:{proxy['port']}/"
                proxies_list.append(url)
            data = {"proxies_list": proxies_list, "time": datetime.utcnow().isoformat()}
            with open(file_path, "w") as json_file:
                json.dump(data, json_file)
            return proxies_list
        else:
            return data.get("proxies_list")
    except Exception as err:
        print(colored(f"[-] Error: {str(err)}", "red"))


def get_video_duration(video_id):
    try:
        # Set up YouTube API client
        youtube = build("youtube", "v3", developerKey=GOOGLE_CONSOLE_API_KEY)
        # Call the videos().list method to retrieve video details
        request = youtube.videos().list(part="contentDetails", id=video_id)
        response = request.execute()
        # Extract duration from the response
        if "items" in response and len(response["items"]) > 0:
            duration = response["items"][0]["contentDetails"]["duration"]
            # Convert duration from ISO 8601 format to more readable format
            duration_obj = re.match(r"PT(\d+H)?(\d+M)?(\d+S)?", duration).groups()
            hours = int(duration_obj[0][:-1]) if duration_obj[0] else 0
            minutes = int(duration_obj[1][:-1]) if duration_obj[1] else 0
            seconds = int(duration_obj[2][:-1]) if duration_obj[2] else 0

            total_seconds = hours * 3600 + minutes * 60 + seconds
            return total_seconds
        else:
            return 0
    except HttpError as e:
        print(
            f"An HTTP error get_video_duration {e.resp.status} occurred:\n{e.content}"
        )
        return 0


def extract_video_id(url):
    try:
        parsed_url = urlparse(url)
        if parsed_url.hostname in ("youtu.be", "www.youtu.be"):
            return parsed_url.path[1:]
        if parsed_url.hostname in ("youtube.com", "www.youtube.com"):
            if parsed_url.path == "/watch":
                return parse_qs(parsed_url.query)["v"][0]
            if parsed_url.path.startswith(("/embed/", "/v/")):
                return parsed_url.path.split("/")[2]
        return None
    except ValueError as e:
        logger.error(f"Error extract_video_id: {str(e)}")
        return None


def validate_youtube_link(link):
    youtube_regex = r"^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.?be)\/.+$"
    if not re.match(youtube_regex, link):
        raise BadRequest("Invalid YouTube link")
    video_id = extract_video_id(link)
    if not video_id:
        logger.error("YouTube video ID not found")
        raise NotFound("YouTube video ID not found")
    duration = get_video_duration(video_id)
    logger.info(f"video duration {duration}")
    if duration > 1800:  # 30 minutes = 1800 seconds
        raise BadRequest("Video length should be less than 30 minutes")
    return link, video_id


class YouTubeTranscriptionFetcher:
    def __init__(self, max_retries=2):
        """
        Initializes the YouTubeTranscriptionFetcher class.
        Args:
        max_retries (int): Maximum number of retry attempts (default is 2).
        """
        self.max_retries = max_retries

    async def get_transcription(self, video_url):
        """
        Fetches the transcription of a YouTube video.
        Args:
        video_url (str): The URL of the YouTube video.

        Returns:
        str: The transcription text of the video.

        Raises:
        ValueError: If the YouTube link is invalid.
        NotFound: If the YouTube video ID is not found.
        TranscriptsDisabled: If transcripts are disabled for the video.
        NoTranscriptFound: If no transcript is available for the video.
        Exception: For other unexpected errors.
        """
        proxy_lists = proxy_list()
        print("proxy_lists === ", proxy_lists)
        link, video_id = validate_youtube_link(video_url)
        transcript = YouTubeTranscriptApi.get_transcript(
            video_id,
            proxies={
                "https": random.choice(proxy_lists),
                "http": random.choice(proxy_lists),
            },
        )

        buffer = io.StringIO()
        for entry in transcript:
            buffer.write(entry["text"])
            buffer.write(" ")
        transcript_text = buffer.getvalue()
        return str(transcript_text)
        # for attempt in range(self.max_retries):
        #     try:
        #         print("proxy_lists === ", proxy_lists)
        #         link, video_id = validate_youtube_link(video_url)
        #         transcript = YouTubeTranscriptApi.get_transcript(
        #             video_id,
        #             proxies={
        #                 "https": random.choice(proxy_lists),
        #                 "http": random.choice(proxy_lists),
        #             },
        #         )

        #         buffer = io.StringIO()
        #         for entry in transcript:
        #             buffer.write(entry["text"])
        #             buffer.write(" ")
        #         transcript_text = buffer.getvalue()
        #         return str(transcript_text)
        #     except Exception as e:
        #         logger.error(f"Attempt {attempt + 1} failed: {str(e)}")
        #         if attempt < self.max_retries - 1:
        #             wait_time = 2  # Exponential backoff
        #             logger.info(f"Retrying in {wait_time} seconds...")
        #             time.sleep(wait_time)
        #         else:
        #             logger.error("Max retries reached. Unable to fetch transcript.")


# "4hus76dw48n1j9m6vd0yg6pggmvko56ejt4ev728"


#####################################################################################################################


def download_youtube_video(
    video_url,
    output_path="./temp/",
    max_retries=4,
    delay_range=(3, 10),
):
    # Create output directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)

    # Base options
    ydl_opts = {
        "format": "bestvideo[height=720][width=1280][ext=mp4]+bestaudio[ext=m4a]/best[height=720][width=1280][ext=mp4]/best[ext=mp4]/best",
        "outtmpl": os.path.join(output_path, "%(title)s.%(ext)s"),
        "quiet": False,
        "no_warnings": True,
        "ignoreerrors": False,
        "extract_flat": False,
        "nocheckcertificate": True,
        "prefer_ffmpeg": True,
        "geo_bypass": True,
        "socket_timeout": 30,
        "extractor_args": {"youtube": "player_client=ios"},
        "http_headers": {
            "Accept-Language": "en-US,en;q=0.9",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
        },
    }

    if os.path.exists("./asset/www.youtube.com_cookies.txt"):
        ydl_opts["cookiefile"] = "./asset/www.youtube.com_cookies.txt"

    retries = 0
    last_error = None

    while retries < max_retries:
        try:
            # Update dynamic options for each attempt
            current_opts = ydl_opts.copy()
            current_opts["proxy"] = (
                random.choice(proxy_list()) if "proxy_list" in globals() else None
            )

            current_opts["geo_verification_proxy"] = proxy_list()[0]

            print("current_opts ==>>", current_opts)

            with yt_dlp.YoutubeDL(current_opts) as ydl:
                # First try to extract info without downloading
                print(
                    f"Attempt {retries + 1}/{max_retries}: Extracting video information..."
                )
                info = ydl.extract_info(video_url, download=False)

                if not info:
                    raise Exception("Failed to extract video information")

                available_formats = info.get("formats", [])

                # Find the target format
                target_format = next(
                    (
                        f
                        for f in available_formats
                        if f.get("width") == 1280 and f.get("height") == 720
                    ),
                    None,
                )

                if target_format:
                    current_opts["format"] = (
                        f"{target_format['format_id']}+bestaudio[ext=m4a]/best[ext=mp4]/best"
                    )
                    print(f"Found 1280x720 format: {target_format['format_id']}")
                else:
                    print(
                        "Exact 1280x720 format not found. Using best available format."
                    )

                # Perform the actual download with updated format
                info = ydl.extract_info(video_url, download=True)
                filename = ydl.prepare_filename(info)

                print(f"Download completed successfully: {filename}")
                return filename

        except yt_dlp.utils.DownloadError as e:
            last_error = str(e)
            print(f"An unexpected error occurred: {last_error}")
        except Exception as e:
            last_error = str(e)
            print(f"An unexpected error occurred: {last_error}")
        retries += 1
        if retries < max_retries:
            delay = random.uniform(*delay_range)
            print(
                f"Attempt {retries}/{max_retries} failed. Retrying in {delay:.2f} seconds..."
            )
            time.sleep(delay)
            # Increase delay range for subsequent retries
            delay_range = (delay_range[0] * 1.5, delay_range[1] * 1.5)

    print(f"Maximum number of retries reached. Last error: {last_error}")
    return None


##################################################################################################3
def youtube_auth(youtube_credentials):

    old_credentials = youtube_credentials
    # # Get the authenticated YouTube service
    credentials = Credentials(
        token=youtube_credentials.get("token"),
        refresh_token=youtube_credentials.get("refresh_token"),
        token_uri=youtube_credentials.get("token_uri"),
        client_id=GOOGLE_CLIENT_ID,
        client_secret=GOOGLE_CLIENT_SECRET,
        scopes=youtube_credentials.get("scopes"),
    )

    # Calculate the refresh token expiration time
    # Assuming the refresh token doesn't expire, set it to 30 days from now
    refresh_token_expires_at = datetime.now() + timedelta(days=7)

    if not credentials.valid:
        if credentials.expired and credentials.refresh_token:
            credentials.refresh(Request())
            youtube_credentials = {
                "token": credentials.token,
                "refresh_token": credentials.refresh_token,
                "token_uri": credentials.token_uri,
                "scopes": credentials.scopes,
                "expires_at": credentials.expiry.isoformat(),
                "refresh_token_expires_at": old_credentials.get(
                    "refresh_token_expires_at", refresh_token_expires_at.isoformat()
                ),
            }
        else:
            return {"message": "Token expired. Please reauthenticate."}, 401

    youtube = build(
        "youtube",
        "v3",
        credentials=credentials,
    )

    return youtube


def get_trending_videos_and_topics(youtube_credentials):
    youtube = youtube_auth(youtube_credentials)

    try:
        # Get trending videos
        videos_request = youtube.videos().list(
            part="snippet,statistics",
            chart="mostPopular",
            regionCode="US",  # Change this to get trending videos for a specific country
            maxResults=10,  # Adjust this to get more or fewer results
            videoCategoryId=28,
        )
        videos_response = videos_request.execute()

        # Get trending topics (guideCategories)
        topics_request = youtube.videoCategories().list(
            part="snippet",
            regionCode="US",  # Change this to get trending topics for a specific country
            hl="en",  # Change this for different languages
        )

        topics_response = topics_request.execute()

        # Process and return the results
        trending_videos = [
            {
                "title": item["snippet"]["title"],
                "description": item["snippet"]["description"],
                "publish_time": item["snippet"]["publishedAt"],
                "channel_title": item["snippet"]["channelTitle"],
                "view_count": item["statistics"]["viewCount"],
                "like_count": item["statistics"].get("likeCount", None),
                "comment_count": item["statistics"].get("commentCount", None),
                "video_id": item["id"],
                "thumbnail": item["snippet"]["thumbnails"]
                .get("maxres", None)
                .get("url", None),
            }
            for item in videos_response["items"]
        ]

        trending_topics = [
            {"title": item["snippet"]["title"], "id": item["id"]}
            for item in topics_response["items"]
        ]

        return {"trending_videos": trending_videos, "trending_topics": trending_topics}

    except HttpError as e:
        print(f"An HTTP error {e.resp.status} occurred:\n{e.content}")
        return None
