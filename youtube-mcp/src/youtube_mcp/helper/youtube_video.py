import io
from googleapiclient.discovery import build
from youtube_transcript_api import YouTubeTranscriptApi
import os
from typing import List, Dict, Any
from ..constant.config import GOOGLE_CONSOLE_API_KEY
from urllib.parse import urlparse, parse_qs
import logging

logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s",
    level=logging.ERROR,
)
logger = logging.getLogger(__name__)


def extract_video_id(url):
    try:
        parsed_url = urlparse(url)
        if parsed_url.hostname in ("youtu.be", "www.youtu.be"):
            return parsed_url.path[1:]
        if parsed_url.hostname in ("youtube.com", "www.youtube.com"):
            if parsed_url.path == "/watch":
                return parse_qs(parsed_url.query)["v"][0]
            if parsed_url.path.startswith(("/embed/", "/v/")):
                return parsed_url.path.split("/")[2]
        return None
    except ValueError as e:
        logger.error(f"Error extract_video_id: {str(e)}")
        return None


def get_transcript(video_url: str, lang: str = None) -> List[Dict[str, Any]]:
    try:
        if lang is None:
            video_id = extract_video_id(video_url)
            lang = os.environ.get("YOUTUBE_TRANSCRIPT_LANG", "en")
            transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=[lang])
            # return transcript
            buffer = io.StringIO()
            for entry in transcript:
                buffer.write(entry["text"])
                buffer.write(" ")
            transcript_text = buffer.getvalue()
            return transcript_text
    except Exception as error:
        raise Exception(f"Failed to retrieve transcript: {str(error)}")


def get_related_videos(video_url: str, max_results: int = 10) -> List[Dict[str, Any]]:
    """
    Get videos related to a specific video using YouTube Data API v3.

    Args:
        video_id: YouTube video ID to find related videos for
        max_results: Maximum number of results to return
        api_key: Your YouTube Data API key (required)

    Returns:
        List of related video items
    """
    # if not api_key:
    #     raise ValueError("API key is required")

    # Important: Use cache_discovery=False to avoid credential detection issues
    youtube = build(
        "youtube", "v3", developerKey=GOOGLE_CONSOLE_API_KEY, cache_discovery=False
    )

    video_id = extract_video_id(video_url)

    try:
        # First get video details to use for finding related content
        video_response = youtube.videos().list(part="snippet", id=video_id).execute()

        if not video_response.get("items"):
            return []

        video_info = video_response["items"][0]["snippet"]
        video_title = video_info.get("title", "")

        # Search for related videos using the title as query
        search_response = (
            youtube.search()
            .list(
                part="snippet",
                type="video",
                maxResults=max_results + 1,  # Get extra to filter original
                q=video_title,
            )
            .execute()
        )

        # Filter out the original video if it appears in results
        related_videos = [
            item
            for item in search_response.get("items", [])
            if item["id"].get("videoId") != video_id
        ]

        # Limit to requested number
        return str(related_videos[:max_results])

    except Exception as error:
        import traceback

        error_details = str(error)
        trace = traceback.format_exc()
        raise Exception(f"Failed to retrieve related videos: {error_details}")


def get_video_engagement_ratio(video_url: str) -> Dict[str, Any]:
    """
    Calculate engagement ratio for a YouTube video.

    Args:
        video_id: YouTube video ID
        api_key: Your YouTube Data API key

    Returns:
        Dict with view count, like count, comment count, and engagement ratio
    """

    # Create YouTube client with explicit API key and disable cache discovery
    youtube = build(
        "youtube", "v3", developerKey=GOOGLE_CONSOLE_API_KEY, cache_discovery=False
    )
    video_id = extract_video_id(video_url)

    try:
        response = youtube.videos().list(part="statistics", id=video_id).execute()

        if not response.get("items"):
            raise ValueError("Video not found.")

        stats = response["items"][0].get("statistics", {})
        view_count = int(stats.get("viewCount", 0))
        like_count = int(stats.get("likeCount", 0))
        comment_count = int(stats.get("commentCount", 0))

        engagement_ratio = (
            f"{((like_count + comment_count) / view_count * 100):.2f}%"
            if view_count > 0
            else "0%"
        )

        return str(
            {
                "viewCount": view_count,
                "likeCount": like_count,
                "commentCount": comment_count,
                "engagementRatio": engagement_ratio,
            }
        )
    except Exception as error:
        raise Exception(f"Failed to calculate video engagement ratio: {str(error)}")


def get_trending_videos(
    region_code: str,
    category_id: str = None,
    max_results: int = 10,
) -> List[Dict[str, Any]]:
    """
    Get trending videos from YouTube for a specific region and category.

    Args:
        api_key: Your YouTube Data API key
        region_code: ISO 3166-1 alpha-2 country code (default: 'US')
        category_id: YouTube video category ID (optional)
        max_results: Maximum number of results to return (default: 10)

    Returns:
        List of trending video details
    """
    # Create YouTube client with explicit API key and disable cache discovery
    youtube = build(
        "youtube", "v3", developerKey=GOOGLE_CONSOLE_API_KEY, cache_discovery=False
    )

    try:
        params = {
            "part": "snippet,statistics",
            "chart": "mostPopular",
            "regionCode": region_code,
            "maxResults": max_results,
        }

        if category_id:
            params["videoCategoryId"] = category_id

        response = youtube.videos().list(**params).execute()

        return str(
            [
                {
                    "id": video.get("id"),
                    "title": video.get("snippet", {}).get("title"),
                    "channelTitle": video.get("snippet", {}).get("channelTitle"),
                    "publishedAt": video.get("snippet", {}).get("publishedAt"),
                    "viewCount": video.get("statistics", {}).get("viewCount"),
                    "likeCount": video.get("statistics", {}).get("likeCount"),
                }
                for video in response.get("items", [])
            ]
        )
    except Exception as error:
        raise Exception(f"Failed to retrieve trending videos: {str(error)}")
