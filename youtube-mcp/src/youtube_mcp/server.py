from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
from pydantic import AnyUrl
import mcp.server.stdio
import json
import uvicorn
import logging
from starlette.applications import Starlette
from starlette.routing import Route
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware
from mcp.server.sse import SseServerTransport
from .helper.youtube_tool import (
    YouTubeTranscriptionFetcher,
    download_youtube_video,
    get_trending_videos_and_topics,
)
from .constant.enum import Tools
from .constant.constants import Descriptions
from .constant.schema import Get_YT_link, Get_REGION_CODE, TRENDING_TOPIC
from .helper.youtube_video import (
    get_related_videos,
    get_transcript,
    get_trending_videos,
    get_video_engagement_ratio,
)

from .helper.config import HOST, PORT

# Store notes as a simple key-value dict to demonstrate state management
notes: dict[str, str] = {}

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Store notes as a simple key-value dict to demonstrate state management
notes: dict[str, str] = {}

server = Server("youtube-mcp")


@server.list_resources()
async def handle_list_resources() -> list[types.Resource]:
    """
    List available note resources.
    Each note is exposed as a resource with a custom note:// URI scheme.
    """
    return [
        types.Resource(
            uri=AnyUrl(f"note://internal/{name}"),
            name=f"Note: {name}",
            description=f"A simple note named {name}",
            mimeType="text/plain",
        )
        for name in notes
    ]


@server.read_resource()
async def handle_read_resource(uri: AnyUrl) -> str:
    """
    Read a specific note's content by its URI.
    The note name is extracted from the URI host component.
    """
    if uri.scheme != "note":
        raise ValueError(f"Unsupported URI scheme: {uri.scheme}")

    name = uri.path
    if name is not None:
        name = name.lstrip("/")
        return notes[name]
    raise ValueError(f"Note not found: {name}")


@server.list_prompts()
async def handle_list_prompts() -> list[types.Prompt]:
    """
    List available prompts.
    Each prompt can have optional arguments to customize its behavior.
    """
    return [
        types.Prompt(
            name="summarize-notes",
            description="Creates a summary of all notes",
            arguments=[
                types.PromptArgument(
                    name="style",
                    description="Style of the summary (brief/detailed)",
                    required=False,
                )
            ],
        )
    ]


@server.get_prompt()
async def handle_get_prompt(
    name: str, arguments: dict[str, str] | None
) -> types.GetPromptResult:
    """
    Generate a prompt by combining arguments with server state.
    The prompt includes all current notes and can be customized via arguments.
    """
    if name != "summarize-notes":
        raise ValueError(f"Unknown prompt: {name}")

    style = (arguments or {}).get("style", "brief")
    detail_prompt = " Give extensive details." if style == "detailed" else ""

    return types.GetPromptResult(
        description="Summarize the current notes",
        messages=[
            types.PromptMessage(
                role="user",
                content=types.TextContent(
                    type="text",
                    text=f"Here are the current notes to summarize:{detail_prompt}\n\n"
                    + "\n".join(
                        f"- {name}: {content}" for name, content in notes.items()
                    ),
                ),
            )
        ],
    )


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """
    List available tools.
    Each tool specifies its arguments using JSON Schema validation.
    """
    return [
        types.Tool(
            name=Tools.FETCH_YT_TRANSCRIPT,
            description=Descriptions.GET_YT_TRANSCRIPT_DESCRIPTION,
            inputSchema=Get_YT_link.schema(),
        ),
        types.Tool(
            name=Tools.DOWNLOAD_YT_VIDEO,
            description=Descriptions.DOWNLOAD_YT_VIDEO_DESCRIPTION,
            inputSchema=Get_YT_link.schema(),
        ),
        types.Tool(
            name=Tools.FETCH_TRENDING_VIDEOS_AND_TOPICS,
            description=Descriptions.GET_TRENDING_VIDEO_TOPIC_DESCRIPTION,
            inputSchema=TRENDING_TOPIC.schema(),
        ),
        types.Tool(
            name=Tools.FETCH_TRANSCRIPT,
            description=Descriptions.GET_VIDEO_TRANSCRIPT_DESCRIPTION,
            inputSchema=Get_YT_link.schema(),
        ),
        types.Tool(
            name=Tools.FETCH_RELATED_VIDEOS,
            description=Descriptions.GET_RELATED_VIDEOS_DESCRIPTION,
            inputSchema=Get_YT_link.schema(),
        ),
        types.Tool(
            name=Tools.CALCULATE_VIDEO_ENGAGEMENT_RATIO,
            description=Descriptions.GET_VIDEO_ENGAGEMENT_RATIO_DESCRIPTION,
            inputSchema=Get_YT_link.schema(),
        ),
        types.Tool(
            name=Tools.FETCH_TRENDING_VIDEOS,
            description=Descriptions.GET_TRENDING_VIDEOS_DESCRIPTION,
            inputSchema=Get_REGION_CODE.schema(),
        ),
    ]


@server.call_tool()
async def handle_call_tool(
    name: str, arguments: dict | None
) -> list[types.TextContent | types.ImageContent | types.EmbeddedResource]:
    """
    Handle tool execution requests.
    Tools can modify server state and notify clients of changes.
    """

    if not arguments:
        raise ValueError("Missing arguments")

    try:

        match name:
            case Tools.FETCH_YT_TRANSCRIPT:

                video_url = arguments.get("video_url")
                response = await YouTubeTranscriptionFetcher().get_transcription(
                    video_url
                )

                result = {"data": response}

            case Tools.DOWNLOAD_YT_VIDEO:

                video_url = arguments.get("video_url")
                response = download_youtube_video(video_url)
                result = {"data": response}

            case Tools.FETCH_TRENDING_VIDEOS_AND_TOPICS:

                video_url = arguments.get("video_url")
                response = get_trending_videos_and_topics(video_url)
                result = {"data": response}

            case Tools.FETCH_TRANSCRIPT:

                video_url = arguments.get("video_url")
                response = get_transcript(video_url)

                result = {"data": response}

            case Tools.FETCH_RELATED_VIDEOS:

                video_url = arguments.get("video_url")
                response = get_related_videos(video_url)

                result = {"data": response}

            case Tools.CALCULATE_VIDEO_ENGAGEMENT_RATIO:

                video_url = arguments.get("video_url")
                response = get_video_engagement_ratio(video_url)

                result = {"data": response}

            case Tools.FETCH_TRENDING_VIDEOS:

                video_url = arguments.get("video_url")
                response = get_trending_videos(video_url)

                result = {"data": response}
            case _:
                return [types.TextContent(type="text", text=f"Unknown tool: {name}")]

    except Exception as error:
        print("Error: ", error)
        error = {"message": f"Error: {str(error)}", "is_error": True}
        return [types.TextContent(type="text", text=json.dumps(error, indent=2))]

    print("result", result)

    return [types.TextContent(type="text", text=json.dumps(result, indent=2))]


async def main():
    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="youtube-mcp",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )


def create_app():

    sse = SseServerTransport("/youtube")

    class HandleSSE:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            async with self.sse.connect_sse(scope, receive, send) as streams:
                await server.run(
                    streams[0],
                    streams[1],
                    server.create_initialization_options(),
                )

    class HandleMessages:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            await self.sse.handle_post_message(scope, receive, send)

    routes = [
        Route("/sse", endpoint=HandleSSE(sse), methods=["GET"]),
        Route("/youtube", endpoint=HandleMessages(sse), methods=["POST"]),
    ]

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
        )
    ]

    return Starlette(routes=routes, middleware=middleware)


def start_server():
    app = create_app()
    logger.info(f"Starting server at {HOST}:{PORT}")
    uvicorn.run(app, host=HOST, port=PORT, timeout_keep_alive=120)


if __name__ == "__main__":
    while True:
        try:
            start_server()
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
            break
        except Exception as e:
            logger.error(f"Server crashed with error: {e}")
            continue
