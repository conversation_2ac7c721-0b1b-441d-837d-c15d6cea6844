from pydantic import BaseModel, HttpUrl, constr, Field


class Get_YT_link(BaseModel):
    video_url: constr(min_length=1, max_length=1000) = Field(
        ..., description="video_url is required"
    )


class Get_REGION_CODE(BaseModel):
    region_code: constr(min_length=1, max_length=50) = Field(
        ..., description="region_code is required"
    )


class TRENDING_TOPIC(BaseModel):
    youtube_credentials: constr(min_length=1, max_length=1000) = Field(
        ..., description="Script is required"
    )
