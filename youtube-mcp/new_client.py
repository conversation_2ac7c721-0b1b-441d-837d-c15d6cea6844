import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


async def main():
    # Set server parameters
    server_params = StdioServerParameters(
        command="youtube-mcp",  # Path to server.py
        args=[
            "C://Users//INDIA//Desktop//mcp//youtube_mcp//src//youtube_mcp//server.py",
            "youtube-mcp",
            "run",
            "youtube-mcp",
        ],  # Command line arguments (if needed)
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize session
            await session.initialize()

            try:
                # Get list of available tools
                tools = await session.list_tools()

                print(f"Available tools: {tools}")

                # Example of calling a tool
                tool_result = await session.call_tool(
                    "get_transcript",
                    arguments={
                        "video_url": "https://youtu.be/QoKpQMJnBHY?si=k1rC3d-3UT0O2VE1",
                    },
                )

                print(f"Tool execution result: {tool_result}")

            except Exception as e:
                print(f"An error occurred: {e}")


if __name__ == "__main__":
    print("Running")
    asyncio.run(main())


# "video_url": "https://youtu.be/xNVM5UxlFSA?si=hKtMatBdcoXQCyra"
