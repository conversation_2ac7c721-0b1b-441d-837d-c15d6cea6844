
# YouTube MCP Server

A MCP server project

# Overview
The YouTube MCP Server is an AI-powered system designed to automate and enhance YouTube-related tasks. It provides multiple tools, including video transcription, which extracts text from videos, trending topic analysis to identify popular content, video download for saving videos in various formats, and video upload with metadata customization. This server is ideal for content creators, marketers, and automation workflows, enabling efficient content management, analysis, and optimization. Whether for SEO, content planning, or bulk uploading, this MCP server streamlines YouTube operations, saving time and effort while improving productivity.

Let me know if you’d like any refinements!
# Quickstart
## Tools

The server implements 6 tools:

 * GETYT_TRANSCRIPT: Creates transcript of youtube video
        Takes "video link" as required string argument
        Generates transcript of video

* DOWNLOAD_YTVIDEO: Download the youtube video
        Takes "video link" as required string argument
        download the yt video

* GETTRENDING_VIDEO_TOOIC: Get the trending video
        Takes "youtube_credentials", "email" as required string    argument
        get trending videos


# Installation

## 1. Clone the Repository:


### Installation

1. Clone the Repository:
```bash
git remote add origin https://gitlab.rapidinnovation.tech/mcp-server/youtube-mcp.git
cd existing_repo
```
2. Create a Virtual Environment (Recommended):
```bash
python3 -m venv .venv
source .venv/bin/activate  # Linux/macOS
.venv\Scripts\activate  # Windows
```
```bash
3. Run the Server:
uv run client.py (mcp server path)
``` 
# Using uv (recommended)

When using uv no specific installation is needed. We will use uvx to directly run avatar-clone.
# Using PIP
Alternatively, you can install audio via pip:
```bash
pip install youtube_mcp
```
After installation, you can run it as a script using:
```bash
python -m youtube_mcp
```
# Configuration

Add this to your claude_desktop_config.json:

**Using uvx**

```bash
"mcpServers": {
  "image-generation-mcp": {
    "command": "uvx",
    "args": ["youtube-mcp"]
  }
}
```
**Using docker**
 * Note: replace '/Users/<USER>' with the path that you want to be accessible by this tool

```bash
"mcpServers": {
  "image-generation-mcp": {
    "command": "docker",
    "args": ["run", "--rm", "-i", "--mount", "type=bind,src=/Users/<USER>/Users/<USER>", "youtube-mcp"]
  }
}
```

**Using pip installation**
```bash
"mcpServers": {
  "image-generation-mcp": {
    "command": "python",
    "args": ["-m", "youtube-mcp"]
  }
}
```

# Development
Building and Publishing
To prepare the package for distribution:

Sync dependencies and update lockfile:
```bash
uv sync
```
Build package distributions:
```bash
uv build
```
This will create source and wheel distributions in the dist/ directory.

Publish to PyPI:
```bash
uv publish
```

Note: You'll need to set PyPI credentials via environment variables or command flags:

    * Token: --token or UV_PUBLISH_TOKEN

    * Or username/password: --username/UV_PUBLISH_USERNAME and     --password/UV_PUBLISH_PASSWORD

# Debugging
You can use the MCP inspector to debug the server. For uvx installations:
```bash
npx @modelcontextprotocol/inspector uvx youtube-mcp
```
# Build
Docker build:
```bash
docker build -t youtube_mcp .
```
Or if you've installed the package in a specific directory or are developing on it:
```bash
cd /Users/<USER>/Desktop/Projects/ciny/ciny-main/mcp-server/youtube-mcp
npx @modelcontextprotocol/inspector uv run youtube-mcp
```
Running tail -n 20 -f ~/Library/Logs/Claude/mcp*.log will show the logs from the server and may help you debug any issues.

# Using Docker
## Build
Docker build:
```bash
docker build -t youtube-mcp
```
## Run
Docker run:
```bash
docker run -it youtube-mcp

docker run -it --env-file .env youtube-mcp
```

# License
This MCP server is licensed under the MIT License. This means you are free to use, modify, and distribute the software, subject to the terms and conditions of the MIT License. For more details, please see the LICENSE file in the project repository.





## Appendix

Any additional information goes here

