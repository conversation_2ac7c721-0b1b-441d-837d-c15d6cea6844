# ruh.ai Developer Portal: User Flow and Interaction Diagrams

This document outlines the user flow for accessing the ruh.ai Developer Portal and sequence diagrams illustrating key interactions within it.

## User Flow: Accessing the Developer Portal

This diagram shows the typical path a new or existing user takes to sign up/log in and access the Developer Portal sections.

```mermaid
flowchart TD
    A([User Visits Website]) --> B{Has Account?}
    B -->|No| C[Sign Up]
    B -->|Yes| D[Log In]
    C --> E[Enter Details]
    E --> F[Submit Form]
    F --> G{Success?}
    G -->|No| H[Show Error]
    H --> E
    G -->|Yes| I[Logged In]
    D --> J[Enter Credentials]
    J --> K[Submit Login]
    K --> L{Success?}
    L -->|No| M[Show Error]
    M --> J
    L -->|Yes| I
    I --> N[Dashboard]
    N --> O[Developer Portal]
    O --> P{Navigate To}
    P -->|API Keys| Q[API Keys]
    P -->|Logs| R[Logs]
    P -->|Webhooks| S[Webhooks]
    P -->|Events| T[Events]
    P -->|Apps| U[Apps]
    Q --> V[Create/Delete Keys]
    R --> W[Filter Logs]
    S --> X[Add Endpoints]
    T --> Y[View Events]
    U --> Z[Manage Apps]
    O --> AA([Exit])
    Q --> AA
    R --> AA
    S --> AA
    T --> AA
    U --> AA
```

## Authentication Flow Sequence Diagram

This diagram shows the interaction flow when a user logs in and accesses the Developer Portal.

```mermaid
sequenceDiagram
    participant User
    participant Frontend (Browser - ruh.ai)
    participant Backend (Auth Service)
    participant Backend (API/BaaS Service)

    User->>Frontend (Browser - ruh.ai): Enters Login Credentials
    Frontend (Browser - ruh.ai)->>Backend (Auth Service): POST /login (credentials)
    Backend (Auth Service)-->>Frontend (Browser - ruh.ai): 200 OK (Auth Token/Session)
    Frontend (Browser - ruh.ai)-->>User: Display Main Dashboard

    User->>Frontend (Browser - ruh.ai): Clicks "Developer" Link
    Frontend (Browser - ruh.ai)->>Backend (API/BaaS Service): GET /api/developer/overview/stats (with Auth Token)
    Note right of Frontend (Browser - ruh.ai): Request initial stats for overview page
    Backend (API/BaaS Service)-->>Frontend (Browser - ruh.ai): 200 OK (Overview Stats Data)
    Frontend (Browser - ruh.ai)-->>User: Render Developer Portal - Overview Section (displaying stats)
```

## API Key Generation Sequence Diagram

This diagram illustrates the process of generating a new API key in the Developer Portal.

```mermaid
sequenceDiagram
    participant User
    participant Frontend (Dev Portal)
    participant Backend (API/BaaS Service)

    User->>Frontend (Dev Portal): Clicks "API Keys" in Sidebar
    Frontend (Dev Portal)-->>User: Render API Keys Section (list existing keys)

    User->>Frontend (Dev Portal): Clicks "Generate New API Key" Button
    Frontend (Dev Portal)-->>User: Display "Generate Key" Modal/Form

    User->>Frontend (Dev Portal): Enters Key Name, Selects Project, Clicks "Generate"
    Frontend (Dev Portal)->>Backend (API/BaaS Service): POST /api/developer/keys (name, projectId, Auth Token)
    Note right of Frontend (Dev Portal): Request to create a new API key

    Backend (API/BaaS Service)->>Backend (API/BaaS Service): Generate Public Key
    Backend (API/BaaS Service)->>Backend (API/BaaS Service): Generate Secret Key
    Backend (API/BaaS Service)->>Backend (API/BaaS Service): Store Key Details (Name, Public Key, Hashed Secret, UserID, ProjectID)

    Backend (API/BaaS Service)-->>Frontend (Dev Portal): 201 Created (New Key Details: Name, Public Key, **Secret Key**)
    Note right of Backend (API/BaaS Service): Secret Key is returned ONLY on creation

    Frontend (Dev Portal)-->>User: Display Success Message with Public Key & Secret Key (provide copy buttons)

    Frontend (Dev Portal)->>Backend (API/BaaS Service): GET /api/developer/keys (Auth Token)
    Note right of Frontend (Dev Portal): Refresh the list of keys

    Backend (API/BaaS Service)-->>Frontend (Dev Portal): 200 OK (Updated List of Keys)
    Frontend (Dev Portal)-->>User: Update API Keys List Table
```

## Webhook Configuration Sequence Diagram

This diagram shows the process of adding a new webhook endpoint in the Developer Portal.

```mermaid
sequenceDiagram
    participant User
    participant Frontend (Dev Portal)
    participant Backend (API/BaaS Service)

    User->>Frontend (Dev Portal): Clicks "Webhooks" in Sidebar
    Frontend (Dev Portal)-->>User: Render Webhooks Section (list existing endpoints)

    User->>Frontend (Dev Portal): Clicks "Add Webhook Endpoint" Button
    Frontend (Dev Portal)-->>User: Display "Add Endpoint" Modal/Form

    User->>Frontend (Dev Portal): Enters Endpoint URL, Selects Events, Clicks "Add"
    Frontend (Dev Portal)->>Backend (API/BaaS Service): POST /api/developer/webhooks (url, events, Auth Token)
    Note right of Frontend (Dev Portal): Request to create a new webhook endpoint

    Backend (API/BaaS Service)->>Backend (API/BaaS Service): Generate Webhook Signing Secret
    Backend (API/BaaS Service)->>Backend (API/BaaS Service): Store Endpoint Details (URL, Events, Secret, UserID)

    Backend (API/BaaS Service)-->>Frontend (Dev Portal): 201 Created (New Endpoint Details: URL, Events, **Signing Secret**)
    Note right of Backend (API/BaaS Service): Signing Secret is returned ONLY on creation

    Frontend (Dev Portal)-->>User: Display Success Message with Endpoint URL & Signing Secret (provide copy button)

    Frontend (Dev Portal)->>Backend (API/BaaS Service): GET /api/developer/webhooks (Auth Token)
    Note right of Frontend (Dev Portal): Refresh the list of webhooks

    Backend (API/BaaS Service)-->>Frontend (Dev Portal): 200 OK (Updated List of Webhooks)
    Frontend (Dev Portal)-->>User: Update Webhook Endpoints List Table
```
