# Expected to be called whenever pyproject.file changes (i.e., dependabot.yml)
name: Update uv.lock

on:
  pull_request:
    paths:
      - "pyproject.toml"

permissions:
  contents: write
  pull-requests: write

jobs:
  update-lock:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: astral-sh/setup-uv@0c5e2b8115b80b4c7c5ddf6ffdd634974642d182 # v5.4.1
        with:
          python-version: "3.12"
      - run: uv lock
      - uses: stefanzweifel/git-auto-commit-action@e348103e9026cc0eee72ae06630dbe30c8bf7a79 # v5.1.0
        with:
          commit_message: Update uv.lock file
