name: CI

on:
  push:
    branches:
      - main
    tags:
      - "**"
  pull_request: {}

env:
  CI: true
  COLUMNS: 120

permissions:
  contents: read

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: astral-sh/setup-uv@0c5e2b8115b80b4c7c5ddf6ffdd634974642d182 # v5.4.1
        with:
          python-version: "3.12"

      - name: Install dependencies
        run: uv sync --frozen --all-extras --all-packages

      - name: Ensure pip
        run: uv run python -m ensurepip

      - uses: pre-commit/action@2c7b3805fd2a0fd8c1884dcaebf91fc102a13ecd # v3.0.1
        with:
          extra_args: --all-files --verbose
        env:
          SKIP: no-commit-to-branch

  mypy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: astral-sh/setup-uv@0c5e2b8115b80b4c7c5ddf6ffdd634974642d182 # v5.4.1
        with:
          python-version: "3.12"

      - name: Install dependencies
        run: uv sync --frozen --all-extras --all-packages

      - name: Run mypy
        run: uv run --frozen mypy --python-version $UV_PYTHON src

  test:
    name: test on ${{ matrix.python-version }}
    runs-on: ubuntu-latest
    timeout-minutes: 5
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", "3.11", "3.12", "3.13"]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: astral-sh/setup-uv@0c5e2b8115b80b4c7c5ddf6ffdd634974642d182 # v5.4.1
        with:
          python-version: ${{ matrix.python-version }}

      - run: mkdir coverage

      - run: uv run --frozen coverage run -m pytest
        env:
          COVERAGE_FILE: coverage/.coverage.${{ runner.os }}-py${{ matrix.python-version }}-standard

      - name: store coverage files
        uses: actions/upload-artifact@65c4c4a1ddee5b72f698fdd19549f0f0fb45cf08 # v4.6.0
        with:
          name: coverage-${{ matrix.python-version }}
          path: coverage
          include-hidden-files: true

  coverage:
    runs-on: ubuntu-latest
    needs: [test]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: get coverage files
        uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
        with:
          merge-multiple: true
          path: coverage

      - uses: astral-sh/setup-uv@0c5e2b8115b80b4c7c5ddf6ffdd634974642d182 # v5.4.1
        with:
          python-version: "3.12"

      - run: uv sync --frozen
      - run: uv run --frozen coverage combine coverage
      - run: uv run --frozen coverage xml

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@1e68e06f1dbfde0e4cefc87efeba9e4643565303 # v5.1.2
        with:
          token: ${{ secrets.CODECOV_TOKEN }}

      - run: uv run --frozen coverage report --fail-under 83

  # https://github.com/marketplace/actions/alls-green#why used for branch protection checks
  check:
    if: always()
    needs: [lint, test, coverage, mypy]
    runs-on: ubuntu-latest

    steps:
      - name: Decide whether the needed jobs succeeded or failed
        uses: re-actors/alls-green@05ac9388f0aebcb5727afa17fcccfecd6f8ec5fe # v1.2.2
        with:
          jobs: ${{ toJSON(needs) }}
          allowed-skips: test-live

  release:
    needs: [check]
    if: "success() && startsWith(github.ref, 'refs/tags/')"
    runs-on: ubuntu-latest
    environment: release

    permissions:
      id-token: write

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: astral-sh/setup-uv@0c5e2b8115b80b4c7c5ddf6ffdd634974642d182 # v5.4.1
        with:
          python-version: "3.12"

      - name: check GITHUB_REF matches package version
        uses: samuelcolvin/check-python-version@758a13b52c26833cffda0f2ed4f3c9e54d9186d9 # v4.1
        with:
          version_file_path: pyproject.toml

      - run: uv build

      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@67339c736fd9354cd4f8cb0b744f2b82a74b5c70 # v1.12.3
        with:
          skip-existing: true
