# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
    - repo: https://github.com/pre-commit/pre-commit-hooks
      rev: v5.0.0
      hooks:
          - id: trailing-whitespace
          - id: end-of-file-fixer
          - id: check-yaml
          - id: check-added-large-files
    - repo: https://github.com/astral-sh/ruff-pre-commit
      # Ruff version.
      rev: v0.11.5
      hooks:
          # Run the linter.
          - id: ruff
            args: [--fix]
          # Run the formatter.
          - id: ruff-format
