# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - commandOrUrl
    properties:
      commandOrUrl:
        type: string
        description: The MCP server SSE endpoint URL or the command to start the local
          stdio server.
      apiAccessToken:
        type: string
        description: Optional access token for Authorization header.
      ssePort:
        type: number
        description: Optional port for SSE server. Defaults to a random port.
      sseHost:
        type: string
        description: Optional host for SSE server. Defaults to 127.0.0.1.
      env:
        type: object
        description: Additional environment variables for the stdio server.
  commandFunction:
    # A function that produces the CLI command to start the MCP on stdio.
    |-
    (config) => { let command = ['mcp-proxy', config.commandOrUrl]; if (config.ssePort) command.push('--sse-port=' + config.ssePort); if (config.sseHost) command.push('--sse-host=' + config.sseHost); return { command: 'mcp-proxy', args: command, env: config.apiAccessToken ? { API_ACCESS_TOKEN: config.apiAccessToken, ...config.env } : config.env }; }
