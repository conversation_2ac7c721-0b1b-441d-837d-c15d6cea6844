import os
from dotenv import load_dotenv

# Load environment variables from a .env file
load_dotenv()

# Access environment variables and store them in variables
DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")

HOST = os.getenv("HOST", "localhost")
PORT = int(os.getenv("PORT", 5004))


OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")


FREEPIK_API_KEY = os.getenv("FREEPIK_API_KEY")
SERPER_API_KEY = os.getenv("SERPER_API_KEY")

AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
S3_BUCKET_NAME = os.getenv("S3_BUCKET_NAME")
AWS_BASE_URL = os.getenv("AWS_BASE_URL")
# Add more variables as needed
# EXAMPLE_VAR = os.getenv('EXAMPLE_VAR')

# You can now use these variables throughout your project
