import re
import json
import openai

# import google.generativeai as genai
# import anthropic
from openai import OpenAI

from ..constants.enum import Model<PERSON><PERSON><PERSON>
from typing import Tuple, List
from .model_config import (
    default_openai_model,
)
from .config import OPENAI_API_KEY


def generate_response(
    prompt: str,
    provider=ModelProvider.OPENAI,
) -> str:
    """
    Generate a script for a video, depending on the subject of the video.
    Args:
        video_subject (str): The subject of the video.
    Returns:
        str: The response from the AI model.
    """
    try:
        if provider == ModelProvider.OPENAI:
            openai.api_key = OPENAI_API_KEY
            response = openai.chat.completions.create(
                model=default_openai_model,
                messages=[{"role": "user", "content": prompt}],
            )
            token_used = response.usage.total_tokens
            print("openai token_used ===>>", token_used)
            response = response.choices[0].message.content
        # if provider == ModelProvider.GOOGLE:
        #     genai.configure(api_key=GEMINI_API_KEY)
        #     model = genai.GenerativeModel(default_gemini_model)
        #     response_model = model.generate_content(prompt)
        #     print(
        #         "google token_used ===>>",
        #         response_model.usage_metadata.total_token_count,
        #     )
        #     response = response_model.text
        # if provider == ModelProvider.DEEPSEEK:
        #     # for backward compatibility, you can still use `https://api.deepseek.com/v1` as `base_url`.
        #     client = OpenAI(api_key=DEEPSEEK_API, base_url="https://api.deepseek.com")
        #     response = client.chat.completions.create(
        #         model=default_deepseek_model,
        #         messages=[
        #             {"role": "system", "content": "You are a helpful assistant"},
        #             {"role": "user", "content": prompt},
        #         ],
        #         max_tokens=1024,
        #         temperature=0.7,
        #         stream=False,
        #     )
        #     response = response.choices[0].message.content
        # if provider == ModelProvider.ANTHROPIC:
        #     client = anthropic.Anthropic(
        #         api_key=CLAUDE_KEY,
        #     )
        #     message = client.messages.create(
        #         model=default_claude_model,
        #         max_tokens=1024,
        #         messages=[{"role": "user", "content": prompt}],
        #     )
        #     response = message.content
        return response
    except Exception as e:
        print(f"[-] Error: openai generate_response {str(e)}")
        raise


def get_image_search_topics(script, size):
    """
    Generate a list of search terms for each section of the provided script to help find relevant images.

    Args:
        script (str): The script content.

    Returns:
        List[str]: A list of search terms for each section of the script.
    """
    prompt = f"""
    Analyze the following script and divide it into meaningful sections. For each section, provide concise search terms 
    that can help find relevant images on Google. Return the search terms as a list, with each entry corresponding to 
    a section of the script. Each entry should contain at most 3 search terms. add the size of the image in the search term.

    Script:
    {script}
    size: {size}

    Return the search terms as a JSON array of strings.
    """
    response = generate_response(prompt).strip()
    try:
        search_terms = json.loads(response)
        if isinstance(search_terms, list):
            return search_terms
        else:
            raise ValueError("Response is not a valid JSON array.")
    except json.JSONDecodeError as e:
        print(f"[-] Error decoding JSON response: {str(e)}")
        raise
