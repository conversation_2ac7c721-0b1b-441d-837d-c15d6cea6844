import base64
import os
import requests
from pathlib import Path
import json
from dotenv import load_dotenv


# Load environment variables
load_dotenv()


# def read_prompt_generate_image(size="1:1"):
#     with open(r"./content/prompts.json", "r") as file:
#         prompts = json.load(file)

#     response = prompts.get("data", [])

#     prompts = [data["prompts"] for data in response]
#     image_urls = []

#     for index, prompt in enumerate(prompts):
#         print("prompt = ", prompt)
#         print("index = ", index)
#         res = generate_image(prompt, size)
#         data = res["data"][0]["base64"]
#         decode_base64_image(data, f"./temp/image/{index}.jpg")
#         # url = s3_uploader.upload_file(
#         #     "ai_images",
#         #     f"./temp/image/{index}.jpg",
#         #     new_file_name=f"{uuid.uuid4()}.jpg",
#         # )
#         # image_urls.append(url)
#         # break
#         if index == len(prompts):
#             break

#     #return str("Image created Successfully")


def decode_base64_image(
    base64_string,
    output_path="./temp/image/image.jpg",
):
    """
    Convert a base64 string with escaped characters to an image file.

    Parameters:
    base64_string (str): The base64 encoded image string (can contain escaped characters)
    output_path (str): Path where the decoded image should be saved

    Returns:
    str: Path to the saved image file
    """
    try:
        # Remove any quotes if present
        base64_string = base64_string.strip("\"'")
        # Replace escaped forward slashes with regular forward slashes
        base64_string = base64_string.replace("\/", "/")
        # Decode the base64 string
        image_data = base64.b64decode(base64_string)
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_path)
        if output_dir:
            Path(output_dir).mkdir(parents=True, exist_ok=True)
        # Write the binary data to an image file
        with open(output_path, "wb") as f:
            f.write(image_data)
        return output_path
    except base64.binascii.Error:
        raise ValueError("Invalid base64 string")
    except Exception as e:
        raise Exception(f"Error decoding image: {str(e)}")


def generate_image(prompt, size="1:1"):
    url = "https://api.freepik.com/v1/ai/text-to-image"

    if size == "16:9":
        size = "widescreen_16_9"
    if size == "9:16":
        size = "social_story_9_16"
    if size == "1:1":
        size = "square_1_1"
    payload = {
        "prompt": prompt,
        "styling": {"style": "photo"},
        "guidance_scale": 2,
        "image": {"size": size},
        "num_images": 1,
        "seed": 42,
    }
    headers = {
        "x-freepik-api-key": os.getenv("x-freepik-api-key"),
        "Content-Type": "application/json ",
    }
    response = requests.request("POST", url, json=payload, headers=headers)
    response.raise_for_status()
    data = response.json()
    return data
