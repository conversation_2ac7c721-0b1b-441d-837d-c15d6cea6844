from typing import Annotated
import os
import sys

from autogen.agentchat import (
    UserProxyAgent,
    AssistantAgent,
    GroupChat,
    GroupChatManager,
)

from ..helper.utils import store_prompt

from ..helper.model_config import AIConfigManager

from .prompts import (
    ANALYZER_AGENT_PROMPT,
    VIDEO_EDITOR_AGENT_PROMPT,
    IMAGE_PROMPT_GENERATOR_AGENT_PROMPT,
)

# Add the parent directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

config_list = AIConfigManager().get_provider_configs()


llm_config = {
    "timeout": 120,
    "seed": 42,
    "config_list": config_list,
    "temperature": 0.5,  # changing temperature to 0.5 from 0.3
}


user_proxy = UserProxyAgent(
    name="User_proxy",
    human_input_mode="NEVER",
    max_consecutive_auto_reply=5,
    system_message="A human admin who checks that agents are working properly and TERMINA<PERSON> the process when desired output is attained.",
    is_termination_msg=lambda x: x.get("content", "")
    and x.get("content", "").rstrip().endswith("TERMINATE"),
    code_execution_config={
        "last_n_messages": 1,
        "work_dir": "groupchat",
        "use_docker": False,
    },
)

Analyzer_Agent = AssistantAgent(
    name="Analyzer_Agent",
    system_message=ANALYZER_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
)


Video_Editor_Agent = AssistantAgent(
    name="Video_Editor_Agent",
    system_message=VIDEO_EDITOR_AGENT_PROMPT,
    llm_config={"config_list": config_list, "cache_seed": None},
    is_termination_msg=lambda x: x.get("content", "")
    and x.get("content", "").rstrip().endswith("TERMINATE"),
)

image_prompt_generator = AssistantAgent(
    name="image_prompt_generator",
    llm_config=llm_config,
    system_message=IMAGE_PROMPT_GENERATOR_AGENT_PROMPT,
)

prompt_writer = AssistantAgent(
    name="prompt_writer",
    llm_config=llm_config,
    system_message="""you are the prompt writer, Your task to create a JSON file using the provided function write_file and store the array of prompt in this file.
    Reply "TERMINATE" in the end when everything is done.""",
)


@user_proxy.register_for_execution()
@prompt_writer.register_for_llm(description="Get the data and save in to json file")
def write_file(
    data: Annotated[str, "The response from the LLM"],
) -> str:
    store_prompt(prompts=data)
    return "success"


groupchat = GroupChat(
    agents=[
        user_proxy,
        Analyzer_Agent,
        Video_Editor_Agent,
        image_prompt_generator,
        prompt_writer,
    ],
    messages=[],
    max_round=10,
)


search_terms_manager = GroupChatManager(
    groupchat=groupchat,
    name="search_terms_manager",
    llm_config=llm_config,
    is_termination_msg=lambda x: x.get("content", "").find("TERMINATE") >= 0,
)
