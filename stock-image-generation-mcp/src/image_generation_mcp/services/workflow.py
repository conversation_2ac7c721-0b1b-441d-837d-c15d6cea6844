from autogen.cache import Cache
from .agents import user_proxy, groupchat, search_terms_manager
import base64
import random
import uuid
import os
from ..constants.enum import VideoViewType
import requests
from ..helper.model_config import AIConfigManager
from pathlib import Path
import json
from PIL import Image
from io import BytesIO

from autogen.agentchat import (
    GroupChat,
    GroupChatManager,
    Agent,
)


from ..helper.config import FREEPIK_API_KEY
from ..helper.utils import get_mime_type
from ..helper.s3_manager import S3Uploader
from ..helper.llm_call import get_image_search_topics
from ..helper.serper import images_search
from .image_agents import ImageAgentService
from ..helper.multi_model_call import get_images_details
from ..helper.utils import store_image_data
from concurrent.futures import ThreadPoolExecutor, as_completed


s3_uploader = S3Uploader()
config_list = AIConfigManager().get_provider_configs()

llm_config = {
    "timeout": 120,
    "seed": 42,
    "config_list": config_list,
    "temperature": 0.5,  # changing temperature to 0.5 from 0.3
}


def read_prompt_generate_image(size=VideoViewType.PORTRAIT.value):

    try:
        with open("./content/image_prompts.json", "r") as file:
            prompts = json.load(file)

        response = prompts.get("data", [])
        prompts = [data["prompts"] for data in response]

        def process_prompt(index, prompt):
            res = generate_image(prompt, size)
            data = res["data"][0]["base64"]
            decode_base64_image(data, f"./temp/{index}.jpg")
            url = s3_uploader.upload_file(
                "ai_images",
                f"./temp/{index}.jpg",
                new_file_name=f"{uuid.uuid4()}.jpg",
            )
            return index, url

        with ThreadPoolExecutor() as executor:
            futures = [
                executor.submit(process_prompt, index, prompt)
                for index, prompt in enumerate(prompts)
            ]
            for future in as_completed(futures):
                index, url = future.result()
                response[index]["url"] = url
                response[index]["mimetype"] = get_mime_type(url)

        return response

    except Exception as e:
        print(f"Error reading prompts: {str(e)}")
        raise e


def decode_base64_image(
    base64_string,
    output_path="./temp/image.jpg",
):
    """
    Convert a base64 string with escaped characters to an image file.

    Parameters:
    base64_string (str): The base64 encoded image string (can contain escaped characters)
    output_path (str): Path where the decoded image should be saved

    Returns:
    str: Path to the saved image file
    """
    try:
        # Remove any quotes if present
        base64_string = base64_string.strip("\"'")
        # Replace escaped forward slashes with regular forward slashes
        base64_string = base64_string.replace("\/", "/")
        # Decode the base64 string
        image_data = base64.b64decode(base64_string)
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_path)
        if output_dir:
            Path(output_dir).mkdir(parents=True, exist_ok=True)
        # Write the binary data to an image file
        with open(output_path, "wb") as f:
            f.write(image_data)
        return output_path
    except base64.binascii.Error:
        raise ValueError("Invalid base64 string")
    except Exception as e:
        raise Exception(f"Error decoding image: {str(e)}")


def generate_image(prompt, size=VideoViewType.LANDSCAPE.value):

    try:

        url = "https://api.freepik.com/v1/ai/text-to-image"

        if size == VideoViewType.LANDSCAPE.value:
            size = "widescreen_16_9"
        elif size == VideoViewType.PORTRAIT.value:
            size = "social_story_9_16"
        elif size == VideoViewType.SQUARE.value:
            size = "square_1_1"
        else:
            size = "social_story_9_16"

        payload = {
            "prompt": prompt,
            "negative_prompt": "b&w, grayscale, disfigured, bad quality",
            "styling": {"style": "photo"},
            "guidance_scale": 2,
            "image": {"size": size},
            "num_images": 1,
            "seed": 42,
        }

        print("payload", payload)

        headers = {
            "x-freepik-api-key": FREEPIK_API_KEY,
            "Content-Type": "application/json ",
        }
        response = requests.request("POST", url, json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()

        data = data["data"][0]["base64"]
        decode_base64_image(data, "./temp/image.jpg")
        url = s3_uploader.upload_file(
            "ai_images",
            "./temp/image.jpg",
            new_file_name=f"{uuid.uuid4()}.jpg",
        )
        return {url: url}

    except Exception as e:
        print(f"Error generating image: {str(e)}")
        raise e


def convert_image_url_to_base64(image_url, max_pixels=25300000):
    """
    Convert an image from URL to base64 string, with size validation and optional resizing

    Args:
        image_url (str): URL of the image to convert
        max_pixels (int): Maximum allowed pixels (width * height)

    Returns:
        str: Base64 encoded string of the image

    Raises:
        ValueError: If image exceeds maximum allowed pixels
        requests.RequestException: If URL fetch fails
    """
    try:
        # Download image from URL
        response = requests.get(image_url)
        response.raise_for_status()

        # Open image using PIL
        img = Image.open(BytesIO(response.content))

        # Calculate current pixels
        current_pixels = img.width * img.height

        # Check if resizing is needed
        if current_pixels > max_pixels:
            # Calculate scaling factor
            scale = (max_pixels / current_pixels) ** 0.5
            new_width = int(img.width * scale)
            new_height = int(img.height * scale)

            # Resize image
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # Convert to RGB if image is in RGBA mode
        if img.mode == "RGBA":
            img = img.convert("RGB")

        # Save image to BytesIO object
        buffered = BytesIO()
        img.save(buffered, format="JPEG")

        # Encode to base64
        img_str = base64.b64encode(buffered.getvalue()).decode()

        return img_str

    except requests.RequestException as e:
        raise Exception(f"Failed to fetch image from URL: {str(e)}")
    except Exception as e:
        raise Exception(f"Error processing image: {str(e)}")


def upscale_image(image_url, prompt):

    image_base64 = convert_image_url_to_base64(image_url)

    url = "https://api.freepik.com/v1/ai/image-upscaler"

    payload = {
        "image": image_base64,
        "webhook_url": "https://httpbin.org/post",
        "scale_factor": "2x",
        "optimized_for": "standard",
        "prompt": prompt,
        "creativity": 2,
        "hdr": 1,
        "resemblance": 0,
        "fractality": -1,
        "engine": "magnific_sparkle",
    }
    headers = {
        "x-freepik-api-key": FREEPIK_API_KEY,
        "Content-Type": "application/json",
    }

    response = requests.request("POST", url, json=payload, headers=headers)

    response.raise_for_status()
    data = response.json()

    print("data", data)
    return data


async def generate_image_prompts(video_script_with_timestamps):
    cache_seed = random.randint(40, 45)
    with Cache.disk(cache_seed=cache_seed) as cache:
        chat_history = user_proxy.initiate_chat(
            search_terms_manager,
            message=f"""
                This is the content :-
                    video_script_with_transcript: {video_script_with_timestamps}
                """,
            cache=cache,
            max_turns=1,
        )
    print("agent cost ====>>>", chat_history.cost)
    user_proxy.reset()
    groupchat.reset()
    search_terms_manager.reset()


async def generate_ai_image_data(
    subtitles,
    size=VideoViewType.PORTRAIT.value,
):
    try:
        await generate_image_prompts(subtitles)
        image_data = read_prompt_generate_image(size)
        return image_data
    except Exception as e:
        print(f"[-] Error generating Images data: {str(e)}")
        raise Exception("Error generating Images data")


def generate_stock_images(script_with_timestamp, size):

    agent_service = ImageAgentService()

    search_words = get_image_search_topics(script_with_timestamp, size)

    print("search_words", search_words)

    # if link is not None:
    #     images = scrape_article(link)["images"]
    #     if len(images) == 0:
    #         images = images_search(f"{search_words} latest images")
    # else:
    images = images_search(f"{search_words} latest images")

    images_details, image_links = get_images_details(script_with_timestamp, images)

    if len(image_links) == 0:
        images = images_search(f"{search_words} similar latest images")
        images_details, image_links = get_images_details(script_with_timestamp, images)

    inputs = f"""
        1. this is the images details:
            content of images : {images_details} with the image number
        2. this is the video script for that want to create by attaching these images: 
            script with subtitles: {script_with_timestamp}
    """

    def custom_speaker_selection_func(last_speaker: Agent, groupchat: GroupChat):
        groupchat.messages
        if last_speaker is agent_service.user_proxy:
            return agent_service.Analyzer_Agent
        elif last_speaker is agent_service.Analyzer_Agent:
            return agent_service.Video_Editor_Agent
        elif last_speaker is agent_service.Video_Editor_Agent:
            return agent_service.Image_Filter_Agent
        elif last_speaker is agent_service.Image_Filter_Agent:
            return agent_service.Image_Insertion_Output_Agent
        elif last_speaker is agent_service.Image_Insertion_Output_Agent:
            store_image_data(data=last_speaker.last_message()["content"])
            last_speaker.reset()
        else:
            return "auto"

    groupchat = GroupChat(
        agents=[
            agent_service.user_proxy,
            agent_service.Analyzer_Agent,
            agent_service.Video_Editor_Agent,
            agent_service.Image_Filter_Agent,
            agent_service.Image_Insertion_Output_Agent,
        ],
        messages=[],
        max_round=10,
        speaker_selection_method=custom_speaker_selection_func,
    )

    manager = GroupChatManager(
        groupchat=groupchat,
        llm_config={"config_list": config_list, "cache_seed": None},
        is_termination_msg=lambda x: x.get("content", "").find("TERMINATE") >= 0,
    )

    with Cache.disk(cache_seed=44) as cache:
        chat_history = agent_service.user_proxy.initiate_chat(
            manager,
            message=inputs,
            cache=cache,
            max_turns=1,
            summary_method="reflection_with_llm",
            summary_args={
                "summary_prompt": "Return the last agent response message in text format"
            },
        )

    agent_service.user_proxy.reset()
    groupchat.reset()
    manager.reset()

    print("agent cost ====>>>", chat_history.cost)

    print("summery", chat_history.summary)

    return image_links


async def generate_image_data(script, size=VideoViewType.PORTRAIT.value):
    image_links = generate_stock_images(script, size)

    with open("./content/images.json", "r") as file:
        image_data = json.load(file)

    for key, value in image_data.items():
        images = json.loads(key)

    images = sorted(images, key=lambda x: x["atTime"])

    for index, image_data in enumerate(images):
        try:
            image_data["url"] = image_links[image_data["image"] - 1]
        except Exception as e:
            print("Error loading image", str(e))

    return image_data
