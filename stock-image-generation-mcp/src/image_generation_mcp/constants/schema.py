from pydantic import BaseModel, constr, Field


class GenerateStockImage(BaseModel):
    script: constr(min_length=1, max_length=1000) = Field(
        ..., description="Script is required"
    )
    view_type: constr(min_length=1, max_length=50)


class GenerateAIStockImage(BaseModel):
    script: constr(min_length=1, max_length=1000) = Field(
        ..., description="Script is required"
    )
    view_type: constr(min_length=1, max_length=50)


class GenerateImage(BaseModel):
    prompt: constr(min_length=1, max_length=1000) = Field(
        ..., description="Script is required"
    )
    view_type: constr(min_length=1, max_length=50)
