from enum import Enum


class Tools(str, Enum):
    GENERATE_STOCK_IMAGE = "generate_stock_image"
    GENERATE_AI_STOCK_IMAGE = "generate_ai_stock_image"
    FETCH_STOCK_IMAGES = "fetch_stock_images"
    GENERATE_IMAGE = "generate_image"


class VideoViewType(str, Enum):
    LANDSCAPE = "LANDSCAPE"
    PORTRAIT = "PORTRAIT"
    SQUARE = "SQUARE"


class ModelProvider(Enum):
    OPENAI = "openai"
    GOOGLE = "google"
    ANTHROPIC = "anthropic"
    DEEPSEEK = "deepseek"
