import pytest
import asyncio
from image_generation_mcp.services.workflow import generate_ai_image_data


# Fixture to create a temporary git repository for testing
@pytest.fixture
def test(input):
    pass


# Additional tests for MCP server functionality can be added here
def test_mcp_server_functionality():
    # Add your MCP server test code here
    pass


async def test_image_generation():
    res = await generate_ai_image_data(
        """1\n00:00:00,160 --> 00:00:03,272\nSo could Europa's hidden oceans harbor alien life? Well,\n\n2\n00:00:03,376 --> 00:00:06,872\nimagine that. Let's dive into this cosmic mystery.\n\n3\n00:00:07,016 --> 00:00:10,376\nSo is there life on Europa? We're talking about Jupiter's icy moon\n\n4\n00:00:10,408 --> 00:00:14,648\nwith a crust of solid ice. And guess what? Scientists think there's a massive ocean\n\n5\n00:00:14,744 --> 00:00:18,216\nlurking beneath. Now here's the wild part. Europa's hidden\n\n6\n00:00:18,248 --> 00:00:22,040\nocean might hold more water than all our earthly oceans\n\n7\n00:00:22,120 --> 00:00:25,256\nput together. How cool is that?\n\n8\n00:00:25,328 --> 00:00:28,728\nAnd thanks to space exploration, we've learned about Europa's tidal\n\n9\n00:00:28,744 --> 00:00:32,470\nheating got Jupiter's pull keeping its ocean warm and just\n\n10\n00:00:32,510 --> 00:00:36,486\nmaybe creating the right spots for life. Kind of like our ocean\n\n11\n00:00:36,518 --> 00:00:40,358\nfloor vents. Picture this life hanging out in a pitch black\n\n12\n00:00:40,414 --> 00:00:43,638\nocean, evolving away from sunlight. Much like those\n\n13\n00:00:43,694 --> 00:00:47,878\nrugged extremophiles we find on Earth. It's not fantasy,\n\n14\n00:00:47,974 --> 00:00:51,318\nit's a solid science fiction. Maybe that shakes up our ideas about\n\n15\n00:00:51,374 --> 00:00:55,010\nhabitability. Right? So what do you think? Leave a thought in the comments.\n\n16\n00:00:55,150 --> 00:00:58,666\nPlus, if you're craving more about cosmic mysteries and Europa's\n\n17\n00:01:02,730\nsecrets, don't fget to subscribe. Whoa. Did that just blow your\n\n18\n00:01:02,770 --> 00:01:05,610\nmind? Give us a shout if you're ready for more space adventures.\n\n"""
    )

    print("res", res)


if __name__ == "__main__":
    asyncio.run(test_image_generation())
