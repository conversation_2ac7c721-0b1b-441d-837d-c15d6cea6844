[project]
name = "image-generation-mcp"
version = "0.1.0"
description = "A MCP server project"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "autogen-agentchat>=*******",
    "autogen-ext>=*******",
    "boto3>=1.37.14",
    "google-generativeai>=0.8.4",
    "google-search-results>=2.4.2",
    "langchain-openai>=0.3.9",
    "mcp>=1.9.4",
    "mcp-proxy>=0.7.0",
    "numpy>=1.26.4",
    "pyautogen==0.2.28",
    "pytest>=8.3.5",
]
[[project.authors]]
name = "AkashAaglawe22012"
email = "<EMAIL>"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
image-generation-mcp = "image_generation_mcp:main"
