import asyncio
from mcp import Client<PERSON>ession, StdioServerParameters
from mcp.client.stdio import stdio_client


async def main():
    # Set server parameters
    server_params = StdioServerParameters(
        command="image-generation-mcp",  # Path to server.py
        args=[
            "C://Users//INDIA//Desktop//mcp//content-extractor-mcp-MCP-server//image_generation_mcp//src//image_generation_mcp//server.py", 
            "image-generation-mcp",
            "run",
            "image-generation-mcp",
        ],  # Command line arguments (if needed)
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize session
            await session.initialize()

            try:
                # Get list of available tools
                tools = await session.list_tools()

                print(f"Available tools: {tools}")

                # Example of calling a tool
                tool_result = await session.call_tool(
                    "generate_image_prompts",
                    arguments={
                        "script": """AI-powered CV systems are transforming industries, from healthcare to security. Using deep learning, CV enables machines to interpret images, recognize objects, and automate tasks. It enhances autonomous vehicles, medical diagnostics, and surveillance. As AI evolves, CV continues to revolutionize how machines perceive and interact with the world.""",
                    },
                )

                print(f"Tool execution result: {tool_result}")

            except Exception as e:
                print(f"An error occurred: {e}")


if __name__ == "__main__":
    print("Running")
    asyncio.run(main())
