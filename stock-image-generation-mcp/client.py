import asyncio
import json
from contextlib import AsyncExitStack
from pprint import pprint
from typing import Optional
from dotenv import load_dotenv
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from openai import OpenAI
import os

# Load environment variables
load_dotenv()

import os


def convert_to_openai_format(mcp_tools):
    """Convert MCP tools to OpenAI format

    Args:
        mcp_tools (List[Any]): List of MCP tools

    Returns:
        List[Dict]: Tools in OpenAI format
    """

    # Return the tools in OpenAI format
    return [
        {
            "type": "function",
            "function": {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.inputSchema,
            },
        }
        for tool in mcp_tools
    ]


class MCPClient:
    def __init__(self):
        # Initialize session and client objects
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.anthropic = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    async def connect_to_server(self):
        """Connect to an MCP server
        Args:
            server_script_path: Path to the server script (.py or .js)
        """
        # is_python = server_script_path.endswith('.py')
        # is_js = server_script_path.endswith('.js')
        # if not (is_python or is_js):
        #     raise ValueError("Server script must be a .py or .js file")

        # command = "python" if is_python else "node"
        server_params = StdioServerParameters(
            command="python",
            args=[
                "C://Users//INDIA//Desktop//mcp//image_mcp//src//image_mcp//server.py"
            ],
            env=None,
        )
        stdio_transport = await self.exit_stack.enter_async_context(
            stdio_client(server_params)
        )
        self.stdio, self.write = stdio_transport
        self.session = await self.exit_stack.enter_async_context(
            ClientSession(self.stdio, self.write)
        )

        # await self.session.initialize()

        import asyncio

        try:
            await asyncio.wait_for(
                self.session.initialize(), timeout=20
            )  # 10 seconds timeout , timeout=200
            print("Session initialize")
        except asyncio.TimeoutError:
            print("Session initialization timed out!")

    async def process_query(self, query: str) -> str:
        """Process a query using Claude and available tools"""
        messages = [{"role": "user", "content": query}]

        response = await self.session.list_tools()
        available_tools = convert_to_openai_format(response.tools)

        print("available_tools = ", available_tools)

        # Initial Claude API call
        response = self.anthropic.chat.completions.create(
            model="gpt-4o-mini",
            messages=messages,
            tools=available_tools,
            tool_choice="auto",
        )
        # print('response = ',response)
        # Process response and handle tool calls
        tool_results = []
        final_text = []
        response = response.choices[0].message

        if response.content:
            final_text.append(response.content)

        elif response.tool_calls:
            tool_data = response.tool_calls[0].function

            tool_name = tool_data.name
            tool_args = json.loads(tool_data.arguments)

            # Execute tool call
            result = await self.session.call_tool(tool_name, tool_args)
            tool_results.append({"call": tool_name, "result": result})
            final_text.append(f"[Calling tool {tool_name} with args {tool_args}]")

            messages.append({"role": "user", "content": result.content})

            # # Get next response from Claude
            # response = self.anthropic.chat.completions.create(model="gpt-4o",
            # messages=messages,
            # tools=available_tools)

            final_text.append(result.content[0].text)

        return "\n".join(final_text)

    async def chat_loop(self):
        """Run an interactive chat loop"""
        print("\nMCP Client Started!")
        print("Type your queries or 'quit' to exit.")

        while True:
            try:
                query = input("\nQuery: ").strip()

                if query.lower() == "quit":
                    break

                response = await self.process_query(query)
                print("\n" + response)

            except Exception as e:
                print(f"\nError: {str(e)}")

    async def cleanup(self):
        """Clean up resources"""
        await self.exit_stack.aclose()


async def main():
    # serverName= "server.py"
    if len(sys.argv) < 2:
        print("Usage: python client.py <path_to_server_script>")
        sys.exit(1)
    client = MCPClient()
    try:
        await client.connect_to_server()
        await client.chat_loop()
    finally:
        await client.cleanup()


if __name__ == "__main__":
    import sys

    asyncio.run(main())
