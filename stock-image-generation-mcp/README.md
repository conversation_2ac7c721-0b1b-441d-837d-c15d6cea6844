
# image-generation-mcp

A MCP server project

## Overview

The Image Generation MCP Server is designed to create AI-generated images for videos based on video scripts. By analyzing the script, the server selects or generates relevant images to enhance the visual storytelling process. It integrates Freepik resources to ensure high-quality visuals, making it ideal for content creators, marketers, and filmmakers looking to streamline video production. This system automates the image selection and generation process, saving time and effort while ensuring a professional and engaging output. Whether for educational videos, promotional content, or storytelling, this MCP server enhances video quality with AI-driven visuals.

## Components

### Resources

The server implements a simple image storage system with:

- Custom image:// URI scheme for accessing individual images
- Each image resource has a name, description, and image/jpeg mimetype

### Prompts

The server provides a single prompt:

- generate-images: Creates images based on video scripts
  - Takes "script" as required string argument
  - Generates images relevant to the script content

### Tools

The server implements one tool:

- generate_image: Generates and fetches images based on the provided script
  - Takes "script" as required string argument
  - Updates server state and notifies clients of resource changes

## Configuration

### Usage with <PERSON>

Add this to your `claude_desktop_config.json`:

<details>
<summary>Using uvx</summary>

```json
"mcpServers": {
  "image-generation-mcp": {
    "command": "uvx",
    "args": ["image-generation-mcp"]
  }
}
```

</details>

<details>
<summary>Using docker</summary>

- Note: replace '/Users/<USER>' with the path that you want to be accessible by this tool

```json
"mcpServers": {
  "image-generation-mcp": {
    "command": "docker",
    "args": ["run", "--rm", "-i", "--mount", "type=bind,src=/Users/<USER>/Users/<USER>", "image-generation-mcp"]
  }
}
```

</details>

<details>
<summary>Using pip installation</summary>

```json
"mcpServers": {
  "image-generation-mcp": {
    "command": "python",
    "args": ["-m", "image_generation_mcp"]
  }
}
```

</details>

### Usage with [Zed](https://github.com/zed-industries/zed)

Add to your Zed settings.json:

<details>
<summary>Using uvx</summary>

```json
"context_servers": [
  "image-generation-mcp": {
    "command": {
      "path": "uvx",
      "args": ["image-generation-mcp"]
    }
  }
],
```

</details>

<details>
<summary>Using pip installation</summary>

```json
"context_servers": {
  "image-generation-mcp": {
    "command": {
      "path": "python",
      "args": ["-m", "image_generation_mcp"]
    }
  }
},
```

</details>

## Quickstart

### Install

1. Clone the repository:

```bash
git clone https://gitlab.rapidinnovation.tech/mcp-server/**********************-mcp.git
cd **********************-mcp
```

2. Create a virtual environment:

```bash
python -m venv .venv
source .venv/bin/activate  # On Windows use `.venv\Scripts\activate`
```

3. Create a `.env` file using the `.env.example` file:

```bash
cp .env.example .env
```

4. Run the server using `uv` command:

```bash
uv run image-generation-mcp
```

This process will install all dependencies.

### Claude Desktop

On MacOS: `~/Library/Application\ Support/Claude/claude_desktop_config.json`
On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

<details>
  <summary>Development/Unpublished Servers Configuration</summary>
  ```json
  "mcpServers": {
    "image-generation-mcp": {
      "command": "uv",
      "args": [
        "--directory",
        "/Users/<USER>/Desktop/Projects/ciny/ciny-main/mcp-server/image-generation-mcp",
        "run",
        "image-generation-mcp"
      ]
    }
  }
  ```
</details>

<details>
  <summary>Published Servers Configuration</summary>
  ```json
  "mcpServers": {
    "image-generation-mcp": {
      "command": "uvx",
      "args": [
        "image-generation-mcp"
      ]
    }
  }
  ```
</details>

## Development

### Building and Publishing

To prepare the package for distribution:

1. Sync dependencies and update lockfile:

```bash
uv sync
```

2. Build package distributions:

```bash
uv build
```

This will create source and wheel distributions in the `dist/` directory.

3. Publish to PyPI:

```bash
uv publish
```

Note: You'll need to set PyPI credentials via environment variables or command flags:

- Token: `--token` or `UV_PUBLISH_TOKEN`
- Or username/password: `--username`/`UV_PUBLISH_USERNAME` and `--password`/`UV_PUBLISH_PASSWORD`

### Debugging

You can use the MCP inspector to debug the server. For uvx installations:

```bash
npx @modelcontextprotocol/inspector uvx image-generation-mcp
```

Or if you've installed the package in a specific directory or are developing on it:

```bash
cd /Users/<USER>/Desktop/Projects/ciny/ciny-main/mcp-server/image-generation-mcp
npx @modelcontextprotocol/inspector uv run image-generation-mcp
```

Running `tail -n 20 -f ~/Library/Logs/Claude/mcp*.log` will show the logs from the server and may help you debug any issues.

### Using Docker

#### Build

Docker build:

```bash
docker build -t image-generation-mcp .
```

#### Run

Docker run:

```bash
docker run -it image-generation-mcp

docker run -it --env-file .env image-generation-mcp
```

## License

This MCP server is licensed under the MIT License. This means you are free to use, modify, and distribute the software, subject to the terms and conditions of the MIT License. For more details, please see the LICENSE file in the project repository.

