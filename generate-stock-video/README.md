# generate-stock-video MCP server

This MCP server helps to fetch and generate stock video footage.

## Components

### Resources

The server implements a simple note storage system with:

- Custom note:// URI scheme for accessing individual notes
- Each note resource has a name, description, and text/plain mimetype

### Prompts

The server provides a single prompt:

- summarize-notes: Creates summaries of all stored notes
  - Optional "style" argument to control detail level (brief/detailed)
  - Generates prompt combining all current notes with style preference

### Tools

The server implements one tool:

- generate_stock_video: Generates and fetches stock video footage
  - Takes "script" as required string arguments
  - Updates server state and notifies clients of resource changes
- generate_ai_stock_video: Generates and fetches stock video footage
  - Takes "script" as required string arguments
  - Updates server state and notifies clients of resource changes

## Configuration

### Usage with <PERSON> Desktop

Add this to your `claude_desktop_config.json`:

<details>
<summary>Using uvx</summary>

```json
"mcpServers": {
  "generate-stock-video": {
    "command": "uvx",
    "args": ["generate-stock-video"]
  }
}
```

</details>

<details>
<summary>Using docker</summary>

- Note: replace '/Users/<USER>' with the a path that you want to be accessible by this tool

```json
"mcpServers": {
  "generate-stock-video": {
    "command": "docker",
    "args": ["run", "--rm", "-i", "--mount", "type=bind,src=/Users/<USER>/Users/<USER>", "generate-stock-video"]
  }
}
```

</details>

<details>
<summary>Using pip installation</summary>

```json
"mcpServers": {
  "generate-stock-video": {
    "command": "python",
    "args": ["-m", "generate_stock_video"]
  }
}
```

</details>

### Usage with [Zed](https://github.com/zed-industries/zed)

Add to your Zed settings.json:

<details>
<summary>Using uvx</summary>

```json
"context_servers": [
  "generate-stock-video": {
    "command": {
      "path": "uvx",
      "args": ["generate-stock-video"]
    }
  }
],
```

</details>

<details>
<summary>Using pip installation</summary>

```json
"context_servers": {
  "generate-stock-video": {
    "command": {
      "path": "python",
      "args": ["-m", "generate_stock_video"]
    }
  }
},
```

</details>

## Quickstart

### Install

1. Clone the repository:

```bash
git clone https://gitlab.rapidinnovation.tech/mcp-server/**********************-mcp/-/tree/dev?ref_type=heads
cd **********************-mcp
```

2. Create a virtual environment:

```bash
python -m venv .venv
source .venv/bin/activate  # On Windows use `.venv\Scripts\activate`
```

3. Create a `.env` file using the `.env.example` file:

```bash
cp .env.example .env
```

4. Run the server using `uv` command:

```bash
uv run generate-stock-video
```

This process will install all dependencies.

### Claude Desktop

On MacOS: `~/Library/Application\ Support/Claude/claude_desktop_config.json`
On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

<details>
  <summary>Development/Unpublished Servers Configuration</summary>
  ```
  "mcpServers": {
    "generate-stock-video": {
      "command": "uv",
      "args": [
        "--directory",
        "/Users/<USER>/Desktop/Projects/ciny/ciny-main/mcp-server/generate-stock-video",
        "run",
        "generate-stock-video"
      ]
    }
  }
  ```
</details>

<details>
  <summary>Published Servers Configuration</summary>
  ```
  "mcpServers": {
    "generate-stock-video": {
      "command": "uvx",
      "args": [
        "generate-stock-video"
      ]
    }
  }
  ```
</details>

## Development

### Building and Publishing

To prepare the package for distribution:

1. Sync dependencies and update lockfile:

```bash
uv sync
```

2. Build package distributions:

```bash
uv build
```

This will create source and wheel distributions in the `dist/` directory.

3. Publish to PyPI:

```bash
uv publish
```

Note: You'll need to set PyPI credentials via environment variables or command flags:

- Token: `--token` or `UV_PUBLISH_TOKEN`
- Or username/password: `--username`/`UV_PUBLISH_USERNAME` and `--password`/`UV_PUBLISH_PASSWORD`

### Debugging

You can use the MCP inspector to debug the server. For uvx installations:

```bash
npx @modelcontextprotocol/inspector uvx generate-stock-video
```

Or if you've installed the package in a specific directory or are developing on it:

```bash
cd /Users/<USER>/Desktop/Projects/ciny/ciny-main/mcp-server/generate-stock-video
npx @modelcontextprotocol/inspector uv run generate-stock-video
```

Running `tail -n 20 -f ~/Library/Logs/Claude/mcp*.log` will show the logs from the server and may help you debug any issues.

### Using Docker

#### Build

Docker build:

```bash
docker build -t generate-stock-video .
```

#### Run

Docker run:

```bash
docker run -it generate-stock-video

docker run -it --env-file .env generate-stock-video
```
