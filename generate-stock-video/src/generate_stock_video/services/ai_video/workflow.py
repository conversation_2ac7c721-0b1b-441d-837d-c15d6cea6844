from autogen.cache import Cache
from .agents import user_proxy, groupchat, search_terms_manager
import uuid
import json

# import replicate
# from app.helper.s3_manager import S3Uploader
# s3_uploader = S3Uploader()


def generate_video_prompts(video_script_with_timestamps):
    with Cache.disk(cache_seed=44) as cache:
        chat_history = user_proxy.initiate_chat(
            search_terms_manager,
            message=f"""
                This is the content :-
                    video_script_with_transcript: {video_script_with_timestamps}
                """,
            cache=cache,
            max_turns=1,
        )
    print("agent cost ====>>>", chat_history.cost)
    user_proxy.reset()
    groupchat.reset()
    search_terms_manager.reset()


def read_prompt_generate_video(size="1:1"):
    with open("./content/video_prompts.json", "r") as file:
        prompts = json.load(file)

    response = prompts.get("data", [])

    prompts = [data["prompt"] for data in response]

    for index, prompt in enumerate(prompts):
        path = generate_video(prompt, size)
        url = s3_uploader.upload_file(
            "ai_images",
            path,
            new_file_name=f"{uuid.uuid4()}.jpg",
        )

        response[index]["url"] = url

    return response


def generate_video(prompt, size="1:1"):

    if size == "16:9":
        height, width = (720, 480)
    if size == "9:16":
        height, width = (480, 720)
    if size == "1:1":
        height, width = (720, 720)

    input = {"prompt": prompt, "height": height, "width": width}

    output = replicate.run(
        "tencent/hunyuan-video:6c9132aee14409cd6568d030453f1ba50f5f3412b844fe67f78a9eb62d55664f",
        input=input,
    )
    print("output", output)
    with open("/temp/output.mp4", "wb") as file:
        file.write(output.read())

    return True


async def generate_ai_stock_video(script, size=None):
    try:
        generate_video_prompts(script)
        video_data = read_prompt_generate_video(size)
        return video_data
    except Exception as e:
        print(f"[-] Error generate_ai_stock_video: {str(e)}")
        raise Exception("Error in generate_ai_stock_video")


# generate_video_prompts(
#     """Have you ever wondered how one man's journey into exile became one of the world's most enduring stories? Welcome to the vibrant world of the Ramayan, where an epic unfolds as Prince Ram embarks on his banbas, or exile, giving birth to a narrative that would captivate millions over millennia.

# Scene 1: Imagine a kingdom at the peak of prosperity—Ayodhya—under the wise King Dasaratha. Yet, destiny makes a curious turn when Queen Kaikeyi demands a boon. Her request? The exile of her beloved Ram for 14 years. Visuals of a throne room, contrasting emotions on royal faces, a silent drumbeat. As the news spreads, Ayodhya holds its breath. The tension is palpable, the suspense building.

# Scene 2: The journey begins. Picture a serene forest, nature's greens enveloping Ram, his devoted wife Sita, and loyal brother Lakshman. This scene paints a picture of resilience and companionship. Slow zoom on their determined faces. The exile isn’t just physical; it involves a profound inner journey. Reflect on the courage it takes to embrace uncertainty.

# Scene 3: Transitioning into the wilderness, confrontations with cunning sages and mystical beings create trials as much of spirit as of skill. Animations of enchanted forests, unfurling scrolls, and mystical apparitions illustrate these divine encounters. In one poignant moment, Ram befriends Jatayu, the noble bird—a symbol of the alliance between humankind and the natural world.

# Scene 4: As time flows, the narrative weaves through challenges and learnings. The trio builds a community, earning the affection and respect of forest dwellers. Footage of joyous interactions, vibrant tribal life juxtaposed with peaceful meditations—a testament to harmonious coexistence. Delve into how Ram's leadership and empathy become pillars of this impromptu society.

# Scene 5: Finally, a harrowing twist—Sita's abduction by Ravana ignites a quest that transforms banbas into a journey of retrieval and righteousness. The visual crescendo: dramatic animations of Ravana, daunting, yet compelling, symbolizing the looming shadow over their mission. Probe this pivotal juncture, asking ourselves—how far would we go for love and duty?

# Now pause. In 1.5 minutes, we’ve traced a fraction of this epic yet eternal tale—a tale not just about exile but about the human spirit's triumph over adversity. Ram's saga reminds us that what we often perceive as an ending can spark new beginnings. So, how does his journey inspire your own?

# Reflect on the enduring truths of the Ramayan, let them linger with you, and embrace your own path, wherever it may lead."""
# )


# read_prompt_generate_video()
