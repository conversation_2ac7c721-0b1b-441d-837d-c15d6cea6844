import json
from autogen.cache import Cache
from generate_stock_video.services.agents import (
    user_proxy,
    groupchat,
    search_terms_manager,
)

from generate_stock_video.services.stock_video import (
    search_for_stock_videos,
    search_for_stock_videos_on_story_block,
    search_stock_videos,
)

from ..helper.utils import get_mime_type
import random


def fetch_stock_videos(
    search_terms_data,
):
    new_search_terms_data = search_videos(
        search_terms_data,
    )
    return new_search_terms_data


def search_videos(search_terms_data, video_provider="all", it=2, min_dur=5):
    search_terms = [data["search_terms"] for data in search_terms_data.get("data", [])]
    video_urls = []
    new_search_terms_data = []
    for index, search_term in enumerate(search_terms):
        if video_provider == "storyblocks":  # Ensure "storyblocks" is a valid term
            found_urls = search_for_stock_videos_on_story_block(
                search_term, it, min_dur
            )
        elif video_provider == "pexels":  # Ensure "pexels" is a valid term
            found_urls = search_for_stock_videos(search_term, it, min_dur)
        else:
            found_urls = search_stock_videos(search_term, it, min_dur)

        for url in found_urls:
            if url is not None and url not in video_urls:
                video_urls.append(url)
                search_terms_data["data"][index]["url"] = url
                search_terms_data["data"][index]["mimetype"] = get_mime_type(url)
                new_search_terms_data.append(search_terms_data["data"][index])
                break
    return new_search_terms_data


async def generate_stock_video(script):
    try:

        await generate_search_terms(script)

        with open("./content/search_terms.json", "r") as file:
            search_terms_data = json.load(file)

        print("search_terms_data 1", search_terms_data)

        search_terms_data = fetch_stock_videos(
            search_terms_data=search_terms_data,
        )

        print("search_terms_data 2", search_terms_data)

        search_terms_data = sorted(search_terms_data, key=lambda x: x.get("at_time"))

        return search_terms_data

    except Exception as e:
        print("Error in generate_stock_video: ", str(e))
        raise Exception("Error in generate_stock_video")


async def generate_search_terms(video_script_with_timestamps):
    cache_seed = random.randint(40, 45)
    with Cache.disk(cache_seed=cache_seed) as cache:
        chat_history = user_proxy.initiate_chat(
            search_terms_manager,
            message=f"""
                This is the content :-
                    video_script_with_transcript: {video_script_with_timestamps}
                """,
            cache=cache,
            max_turns=1,
        )
    print("agent cost ====>>>", chat_history.cost)
    user_proxy.reset()
    groupchat.reset()
    search_terms_manager.reset()
    return "success"


async def fetch_and_stock_videos(search_terms, video_provider="all", it=2, min_dur=5):
    new_search_terms_data = [{} for _ in search_terms]

    for index, search_term in enumerate(search_terms):
        if video_provider == "storyblocks":
            found_urls = search_for_stock_videos_on_story_block(
                [search_term], it, min_dur
            )
        elif video_provider == "pexels":
            found_urls = search_for_stock_videos(search_term, it, min_dur)
        else:
            found_urls = search_stock_videos(search_term, it, min_dur)

        for url in found_urls:
            if "results" not in new_search_terms_data[index]:
                new_search_terms_data[index]["results"] = []
            new_search_terms_data[index]["results"].append(
                {
                    "search_term": search_term,
                    "url": url,
                    "mimetype": get_mime_type(url),
                }
            )

    return new_search_terms_data
