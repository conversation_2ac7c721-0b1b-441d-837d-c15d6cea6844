from pydantic import BaseModel, HttpUrl, constr, Field


class GenerateStockVideo(BaseModel):
    script: constr(min_length=1, max_length=1000) = Field(
        ..., description="Script is required"
    )


class GenerateAIStockVideo(BaseModel):
    script: constr(min_length=1, max_length=1000) = Field(
        ..., description="Script is required"
    )


class FetchStockVideos(BaseModel):
    search_terms: list[constr(min_length=1, max_length=100)] = Field(
        ..., description="Search terms are required"
    )
    page: int = Field(1, description="Page number for pagination, default is 1", ge=1)
    page_size: int = Field(
        10, description="Number of results per page, default is 10", ge=1
    )
