import json
from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
from pydantic import AnyUrl
import mcp.server.stdio

import uvicorn
import logging

from starlette.applications import Starlette
from starlette.routing import Route

from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware

from mcp.server.sse import SseServerTransport
from generate_stock_video.constants.enum import Tools
from generate_stock_video.constants.constant import Descriptions
from generate_stock_video.constants.schema import (
    GenerateStockVideo,
    GenerateAIStockVideo,
    FetchStockVideos,
)

from generate_stock_video.helper.config import HOST, PORT
from generate_stock_video.services.workflow import (
    generate_stock_video,
    fetch_and_stock_videos,
)
from generate_stock_video.services.ai_video.workflow import generate_ai_stock_video


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Store notes as a simple key-value dict to demonstrate state management
notes: dict[str, str] = {}

server = Server("generate-stock-video")


@server.list_resources()
async def handle_list_resources() -> list[types.Resource]:
    """
    List available note resources.
    Each note is exposed as a resource with a custom note:// URI scheme.
    """
    return [
        types.Resource(
            uri=AnyUrl(f"note://internal/{name}"),
            name=f"Note: {name}",
            description=f"A simple note named {name}",
            mimeType="text/plain",
        )
        for name in notes
    ]


@server.read_resource()
async def handle_read_resource(uri: AnyUrl) -> str:
    """
    Read a specific note's content by its URI.
    The note name is extracted from the URI host component.
    """
    if uri.scheme != "note":
        raise ValueError(f"Unsupported URI scheme: {uri.scheme}")

    name = uri.path
    if name is not None:
        name = name.lstrip("/")
        return notes[name]
    raise ValueError(f"Note not found: {name}")


@server.list_prompts()
async def handle_list_prompts() -> list[types.Prompt]:
    """
    List available prompts.
    Each prompt can have optional arguments to customize its behavior.
    """
    return [
        types.Prompt(
            name="summarize-notes",
            description="Creates a summary of all notes",
            arguments=[
                types.PromptArgument(
                    name="style",
                    description="Style of the summary (brief/detailed)",
                    required=False,
                )
            ],
        )
    ]


@server.get_prompt()
async def handle_get_prompt(
    name: str, arguments: dict[str, str] | None
) -> types.GetPromptResult:
    """
    Generate a prompt by combining arguments with server state.
    The prompt includes all current notes and can be customized via arguments.
    """
    if name != "summarize-notes":
        raise ValueError(f"Unknown prompt: {name}")

    style = (arguments or {}).get("style", "brief")
    detail_prompt = " Give extensive details." if style == "detailed" else ""

    return types.GetPromptResult(
        description="Summarize the current notes",
        messages=[
            types.PromptMessage(
                role="user",
                content=types.TextContent(
                    type="text",
                    text=f"Here are the current notes to summarize:{detail_prompt}\n\n"
                    + "\n".join(
                        f"- {name}: {content}" for name, content in notes.items()
                    ),
                ),
            )
        ],
    )


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """
    List available tools.
    Each tool specifies its arguments using JSON Schema validation.
    """
    return [
        types.Tool(
            name=Tools.GENERATE_STOCK_VIDEO,
            description=Descriptions.GENERATE_STOCK_VIDEO,
            inputSchema=GenerateStockVideo.schema(),
        ),
        types.Tool(
            name=Tools.GENERATE_AI_STOCK_VIDEO,
            description=Descriptions.GENERATE_AI_STOCK_VIDEO,
            inputSchema=GenerateAIStockVideo.schema(),
        ),
        types.Tool(
            name=Tools.FETCH_STOCK_VIDEOS,
            description=Descriptions.FETCH_STOCK_VIDEOS,
            inputSchema=FetchStockVideos.schema(),
        ),
    ]


@server.call_tool()
async def handle_call_tool(
    name: str, arguments: dict | None
) -> list[types.TextContent | types.ImageContent | types.EmbeddedResource]:
    """
    Handle tool execution requests.
    Tools can modify server state and notify clients of changes.
    """
    try:
        match name:
            case Tools.GENERATE_STOCK_VIDEO:

                script = arguments.get("script")

                # response = await generate_stock_video(script)

                response = [
                    {
                        "search_terms": [
                            "Nvidia tech conference footage",
                            "Nvidia brand logo unveiling",
                            "futuristic AI robotics",
                        ],
                        "at_time": 4.024,
                        "url": "https://videos.pexels.com/video-files/8979510/8979510-uhd_3840_2160_30fps.mp4",
                        "mimetype": "video/mp4",
                    },
                    {
                        "search_terms": [
                            "robots performing complex tasks",
                            "Nvidia developer day sessions",
                            "AI quantum computing presentation",
                        ],
                        "at_time": 24.344,
                        "url": "https://videos.pexels.com/video-files/6804126/6804126-uhd_4096_2160_25fps.mp4",
                        "mimetype": "video/mp4",
                    },
                    {
                        "search_terms": [
                            "AI environmental technology projects",
                            "AI composing music digital interface",
                            "AI in climate solutions",
                        ],
                        "at_time": 49.714,
                        "url": "https://videos.pexels.com/video-files/3141208/3141208-uhd_3840_2160_25fps.mp4",
                        "mimetype": "video/mp4",
                    },
                    {
                        "search_terms": [
                            "call-to-action subscribe graphics",
                            "Nvidia future innovations montage",
                            "viewer engagement prompt",
                        ],
                        "at_time": 79.408,
                        "url": "https://videos.pexels.com/video-files/10764524/10764524-hd_1920_1080_30fps.mp4",
                        "mimetype": "video/mp4",
                    },
                ]

                result = {"stock_video_clips": response}

                print("result: ", result)

            case Tools.GENERATE_AI_STOCK_VIDEO:

                script = arguments.get("script")
                response = await generate_ai_stock_video(script)

                result = {"stock_video_clips": response}

            case Tools.FETCH_STOCK_VIDEOS:

                search_terms = arguments.get("search_terms")
                page = arguments.get("page")
                page_size = arguments.get("page_size")
                response = await fetch_and_stock_videos(search_terms, it=page_size)

                result = {"stock_video_clips": response}

            case _:
                return [types.TextContent(type="text", text=f"Unknown tool: {name}")]

    except Exception as error:
        print("Error:", error)
        error = {"message": str(error), "is_error": True}
        return [types.TextContent(type="text", text=json.dumps(error, indent=2))]

    return [types.TextContent(type="text", text=json.dumps(result, indent=2))]


async def main():
    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="generate-stock-video",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
            raise_exceptions=True,
        )


def create_app():

    sse = SseServerTransport("/stock-video")

    class HandleSSE:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            async with self.sse.connect_sse(scope, receive, send) as streams:
                await server.run(
                    streams[0],
                    streams[1],
                    server.create_initialization_options(),
                )

    class HandleMessages:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            await self.sse.handle_post_message(scope, receive, send)

    routes = [
        Route("/sse", endpoint=HandleSSE(sse), methods=["GET"]),
        Route("/stock-video", endpoint=HandleMessages(sse), methods=["POST"]),
    ]

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
        )
    ]

    return Starlette(routes=routes, middleware=middleware)


def start_server():
    app = create_app()
    logger.info(f"Starting server at {HOST}:{PORT}")
    uvicorn.run(app, host=HOST, port=PORT, timeout_keep_alive=120)


if __name__ == "__main__":
    while True:
        try:
            start_server()
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
            break
        except Exception as e:
            logger.error(f"Server crashed with error: {e}")
            continue
