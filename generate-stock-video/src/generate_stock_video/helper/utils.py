import json
import os
import hmac
import hashlib
import requests
import mimetypes

directory = "./content"

if not os.path.exists(directory):
    os.makedirs(directory)


def store_script_title(search_terms):
    """
    Store the script and story title points in a JSON file.
    Args:
        script (str): The script content.
        title_points (list): A list of title points.
        filename (str): The name of the JSON file to store the data.
    """
    data = {"data": json.loads(search_terms)}
    file_name = "search_terms.json"
    file_path = os.path.join(directory, file_name)
    with open(file_path, "w") as json_file:
        json.dump(data, json_file)


def generate_hmac(secret_key, expires):

    # url info
    resource = "/api/v2/videos/search"

    hmacBuilder = hmac.new(
        bytearray(secret_key + expires, "utf-8"),
        resource.encode("utf-8"),
        hashlib.sha256,
    )
    hmacHex = hmacBuilder.hexdigest()

    return hmacHex


def store_prompt(prompts, type="image"):
    """
    Store the script and story title points in a JSON file.
    Args:
        script (str): The script content.
        title_points (list): A list of title points.
        filename (str): The name of the JSON file to store the data.
    """
    data = {"data": json.loads(prompts)}
    file_name = f"{type}_prompts.json"
    file_path = os.path.join(directory, file_name)
    with open(file_path, "w") as json_file:
        json.dump(data, json_file)


def get_mime_type(url):
    # Try to get MIME type from file extension
    mime_type, _ = mimetypes.guess_type(url)
    if mime_type:
        return mime_type
    # Fallback: Fetch headers for Content-Type
    try:
        response = requests.head(url, allow_redirects=True)
        content_type = response.headers.get("Content-Type")
        if content_type:
            return content_type.split(";")[0]  # Remove encoding info if present
    except requests.RequestException:
        pass
    return "unknown/unknown"  # Default if no MIME type is found
