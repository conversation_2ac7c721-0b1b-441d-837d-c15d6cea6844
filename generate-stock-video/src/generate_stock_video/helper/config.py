import os
from dotenv import load_dotenv

# Load environment variables from a .env file
load_dotenv()

# Access environment variables and store them in variables
DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")

HOST = os.getenv("HOST", "localhost")
PORT = int(os.getenv("PORT", 5004))

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
PEXELS_API_KEY = os.getenv("PEXELS_API_KEY")
STORYBLOCKS_API_KEY = os.getenv("STORYBLOCKS_API_KEY")
STORYBLOCKS_SCRECT_KEY = os.getenv("STORYBLOCKS_SCRECT_KEY")
SUTTERSTOCK_TOKEN = os.getenv("SUTTERSTOCK_TOKEN")
GETTY_IMAGES_API_KEY = os.getenv("GETTY_IMAGES_API_KEY")


# Add more variables as needed
# EXAMPLE_VAR = os.getenv('EXAMPLE_VAR')

# You can now use these variables throughout your project
