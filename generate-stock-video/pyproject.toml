[project]
name = "generate-stock-video"
version = "0.1.0"
description = "This mcp server help to fetch and generate the stock video"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "mcp>=1.5.0",
    "pyautogen==0.2.28",
    "requests>=2.32.3",
]
[[project.authors]]
name = "<PERSON><PERSON><PERSON>"
email = "<EMAIL>"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
generate-stock-video = "generate_stock_video:main"
