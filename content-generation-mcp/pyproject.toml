[project]
name = "main-content-generation"
version = "0.1.0"
description = "Blog main content generation "
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "ag2==0.7.6",
    "annotated-types==0.7.0",
    "anyio==4.8.0",
    "asyncer==0.0.8",
    "certifi==2025.1.31",
    "charset-normalizer==3.4.1",
    "click==8.1.8",
    "colorama==0.4.6",
    "diskcache==5.6.3",
    "distro==1.9.0",
    "docker==7.1.0",
    "fast-depends==2.4.12",
    "h11==0.14.0",
    "httpcore==1.0.7",
    "httpx==0.28.1",
    "httpx-sse==0.4.0",
    "idna==3.10",
    "jiter==0.8.2",
    "mcp==1.3.0",
    "openai==1.65.1",
    "packaging==24.2",
    "pyautogen==0.7.6",
    "pydantic==2.10.6",
    "pydantic-core==2.27.2",
    "pydantic-settings==2.8.1",
    "python-dotenv==1.0.1",
    "regex==2024.11.6",
    "requests==2.32.3",
    "sniffio==1.3.1",
    "sse-starlette==2.2.1",
    "starlette==0.46.0",
    "termcolor==2.5.0",
    "tiktoken==0.9.0",
    "tqdm==4.67.1",
    "typing-extensions==4.12.2",
    "urllib3==2.3.0",
    "uvicorn==0.34.0",
]

[[project.authors]]
name = "Aishik"
email = "<EMAIL>"

[build-system]
requires = [ "hatchling",]
build-backend = "hatchling.build"

[project.scripts]
main-content-generation = "main_content_generation:main"
