from .decorators.decorators import sync_to_async
from .config.imports import *
from .config.config import llm_config

load_dotenv()

@sync_to_async
def content_informative(json_points):
    subtopic_writer = AssistantAgent(
        name="Content-Generating-Agent",
        system_message=f"""
        Write content for each points ensuring to add sources of other sites as link only if you add any numbers as facts (Limit to 3).
        Write a maximum of 1000 words. 
        Write content ensuring keywords and LSI keywords are periodically used as per SEO best practises.
        Use clear, concise language. Keep the numbering given to you, do not add any formatting.
        Use plain text for the content under each subheading.
        Keep the content skimmable by adding bullet points (without numbering) to increase readability.
        Ensure proper bullet point formatting (including proper indentation).
        Don't add a summarized/to summarize paragraph.
        """,
        max_consecutive_auto_reply=1,
        llm_config=llm_config,
        human_input_mode="NEVER",
    )

    if isinstance(json_points, str):
        messages = [{"content": json_points, "role": "user"}]
    else:
        messages = json_points  # Assume text is already a list of dictionaries

    response = subtopic_writer.generate_reply(messages=messages)
    return response

@sync_to_async
def content_technical(json_points):
    subtopic_writer = AssistantAgent(
        name="Content-Generating-Agent",
        system_message=f"""
        Write content for each points ensuring to add sources of other sites as link only if you add any numbers as facts (Limit to 3).
        Write a maximum of 600 words. Use clear, concise language. Keep the numbering given to you, do not add any formatting.
        Add codeblocks and steps in bullet points (without numbering) to achieve the final output according to the {json_points} (ONLY ADD IF REQUIRED).
        Write content ensuring keywords and LSI keywords are periodically used as per SEO best practises.
        The content can include technical solutions,facts,information according to need.
        any subheadings you add should have h4 formatting (do not add any other form of heading formatting to the rest of the content.)
        Keep the content skimmable by adding bullet points (without numbering) to increase readability.
        Ensure proper bullet point formatting (including proper indentation).
        Don't add a summarized/to summarize paragraph.
        """,
        max_consecutive_auto_reply=1,
        llm_config=llm_config,
        human_input_mode="NEVER",
    )
    #step1, step2 in prompt
    if isinstance(json_points, str):
        messages = [{"content": json_points, "role": "user"}]
    else:
        messages = json_points  # Assume text is already a list of dictionaries

    response = subtopic_writer.generate_reply(messages=messages)
    return response

@sync_to_async
def toning_agent(blog_content):
    toner_agent = AssistantAgent(
        name="Content-Generating-Agent",
        system_message=f"""
        Rapid Innovation is a AI  development firm offering development and consulting solutions to clients.
        I want to convey to our readers how Rapid Innovation can help them achieve business goals efficiently and effectively.
        Act like a advisor and exhibit your AI expertise in the domain giving examples how we help clients achieve greater ROI.
        Slightly edit the {blog_content} to match the tone of a corporate organisation (Rapid Innovation) who is trying to offer it's services
        The text tone should be domain specific, for AI related blogs the tone should be related to AI and for Blockchain the tone should relate to Blockchain
        If adding a separate section, add it only once to the most relevant position.
        Establish the entity-relationship of the keywords with Rapid Innovation.
        Do not add anything extra irrelevant to the content and make sure to follow the flow of the content to avoid anything looking forcefully added.
        Do not add any hyperlinks in the {blog_content}.
        Do not add/delete the content.
        """, 
        max_consecutive_auto_reply=1,
        llm_config=llm_config,
        human_input_mode="NEVER",
    )

    if isinstance(blog_content, str):
        messages = [{"content": blog_content, "role": "user"}]
    else:
        messages = blog_content  # Assume blog_content is already a list of dictionaries

    response = toner_agent.generate_reply(messages=messages)
    
    
    return response

@sync_to_async
def format_blog_content(blog_content):
    formatted_content = []

    # Split the input string into lines based on newline characters
    lines = blog_content.strip().split('\n')

    for line in lines:
        # Remove leading and trailing whitespace from each line
        line = line.strip()
        if line:
            # Use a regular expression to match patterns like "1.", "1.1", "1.1.", "1.1.1", "1.1.1."
            match = re.match(r'^(\d+(\.\d+)*\.?)\s*(.*)', line)
            if match:
                chapter_section = match.group(1).rstrip('.')
                title = match.group(3)
                levels = chapter_section.split('.')

                # Determine the level of the heading based on the number of periods
                if len(levels) == 1:
                    # It's a chapter
                    formatted_content.append(f"## {chapter_section}. {title}")
                elif len(levels) == 2:
                    # It's a section
                    formatted_content.append(f"### {chapter_section}. {title}")
                elif len(levels) >= 3:
                    # It's a subsection
                    formatted_content.append(f"##### {chapter_section}. {title}")
            else:
                # Regular content line, add it as is
                formatted_content.append(line)

        # Add an empty line after each line to separate paragraphs
        formatted_content.append('')

    # Join the formatted content into a single string with newline characters
    formatted_content_str = '\n'.join(formatted_content)
    return formatted_content_str

@sync_to_async
def format_bullet_points(formatted_content):
  formatter = AssistantAgent(
        name="Formatting-Agent",
        system_message=f"""
        DO NOT change anything in the numbered headings. You are not allowed to edit the heading formatting, Only the content inside it.
        Read the given {formatted_content} and make sure the bullet points are in proper format and the accurate indentation is followed
        for the points given. if you notice many sentences just broken into bullet points that dont make sense, correct the same into a paragraph
        Make the bullet point starter bold. Also check if there is any code in the content that is not properly 
        wrapped in ``` (backticks) and correct the same.
        Ensure Standard MD formatting.
        """,
        max_consecutive_auto_reply=1,
        llm_config=llm_config,
        human_input_mode="NEVER",
        is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
    )


  if isinstance(formatted_content, str):
    messages = [{"content": formatted_content, "role": "user"}]
  else:
    messages = formatted_content  # Assume greeting is already a list of dictionaries
  response = formatter.generate_reply(messages=messages)
  return response
