import os
from dotenv import load_dotenv

load_dotenv()

config_list = [
    {
        'model': 'gpt-4o-mini',
        'api_key': os.getenv("OPENAI_API_KEY"),
        # 'base_url': os.getenv("BASE_URL")
    }
] 

config_list_4 = [
    {
        'model': 'gpt-4-turbo',
        'api_key': os.getenv("OPENAI_API_KEY"),
        # 'base_url': os.getenv("BASE_URL")
    }
] 

config_list_claude = [
    {
        "model": "claude-3-5-sonnet-20240620",
        "api_key": os.getenv("CLAUDE_API_KEY"),
        "api_type": "anthropic",
    }
]          

llm_config = {
    "timeout": 600,
    "cache_seed": 42,
    "seed": 42,
    "config_list": config_list,
    "temperature": 0,
}

llm_config_claude = {
    "timeout": 600,
    "cache_seed": None,
    "seed": 42,
    "config_list": config_list_claude,
    "temperature": 0,
}