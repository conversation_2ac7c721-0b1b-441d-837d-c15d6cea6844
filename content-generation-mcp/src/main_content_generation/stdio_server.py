import asyncio

from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
from pydantic import AnyUrl
import mcp.server.stdio
from .logger import logging
from .tools.subtopic_writer import content_informative, content_technical  ,  toning_agent, format_blog_content, format_bullet_points  

# Store notes as a simple key-value dict to demonstrate state management
notes: dict[str, str] = {}

server = Server("Main-content-generation")

@server.list_resources()
async def handle_list_resources() -> list[types.Resource]:
    """
    List available note resources.
    Each note is exposed as a resource with a custom note:// URI scheme.
    """
    logging.info("handle_list_resources: start")
    try:
        resources = [
            types.Resource(
                uri=AnyUrl(f"note://internal/{name}"),
                name=f"Note: {name}",
                description=f"A simple note named {name}",
                mimeType="text/plain",
            )
            for name in notes
        ]
        logging.info(f"handle_list_resources: resources={resources}")
        return resources
    except Exception as e:
        logging.exception("handle_list_resources: exception")
        raise
    finally:
        logging.info("handle_list_resources: end")


@server.read_resource()
async def handle_read_resource(uri: AnyUrl) -> str:
    """
    Read a specific note's content by its URI.
    The note name is extracted from the URI host component.
    """
    logging.info(f"handle_read_resource: start, uri={uri}")
    try:
        if uri.scheme != "note":
            raise ValueError(f"Unsupported URI scheme: {uri.scheme}")

        name = uri.path
        if name is not None:
            name = name.lstrip("/")
            content = notes[name]
            logging.info(f"handle_read_resource: content found for name={name}")
            return content
        raise ValueError(f"Note not found: {name}")
    except Exception as e:
        logging.exception("handle_read_resource: exception")
        raise
    finally:
        logging.info("handle_read_resource: end")


@server.list_prompts()
async def handle_list_prompts() -> list[types.Prompt]:
    """
    List available prompts.
    Each prompt can have optional arguments to customize its behavior.
    """
    logging.info("handle_list_prompts: start")
    try:
        prompts = [
            types.Prompt(
                name="summarize-notes",
                description="Creates a summary of all notes",
                arguments=[
                    types.PromptArgument(
                        name="style",
                        description="Style of the summary (brief/detailed)",
                        required=False,
                    )
                ],
            )
        ]
        logging.info(f"handle_list_prompts: prompts={prompts}")
        return prompts
    except Exception as e:
        logging.exception("handle_list_prompts: exception")
        raise
    finally:
        logging.info("handle_list_prompts: end")


@server.get_prompt()
async def handle_get_prompt(
    name: str, arguments: dict[str, str] | None
) -> types.GetPromptResult:
    """
    Generate a prompt by combining arguments with server state.
    The prompt includes all current notes and can be customized via arguments.
    """
    logging.info(f"handle_get_prompt: start, name={name}, arguments={arguments}")
    try:
        if name != "summarize-notes":
            raise ValueError(f"Unknown prompt: {name}")

        style = (arguments or {}).get("style", "brief")
        detail_prompt = " Give extensive details." if style == "detailed" else ""

        result = types.GetPromptResult(
            description="Summarize the current notes",
            messages=[
                types.PromptMessage(
                    role="user",
                    content=types.TextContent(
                        type="text",
                        text=f"Here are the current notes to summarize:{detail_prompt}\n\n"
                        + "\n".join(
                            f"- {name}: {content}"
                            for name, content in notes.items()
                        ),
                    ),
                )
            ],
        )
        logging.info(f"handle_get_prompt: result={result}")
        return result
    except Exception as e:
        logging.exception("handle_get_prompt: exception")
        raise
    finally:
        logging.info("handle_get_prompt: end")


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """
    List available tools.
    Each tool specifies its arguments using JSON Schema validation.
    """
    logging.info("handle_list_tools: start")
    try:
        tools = [
            types.Tool(
                name="content-informative",
                description="Generate informative content from input text.",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "input_text": {"type": "string"},
                    },
                    "required": ["input_text"],
                },
            ),
            types.Tool(
                name="content-technical",
                description="Generate technical content from input text. Pass only points no Ebaloration is required , only use points to generate content",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "input_text": {"type": "string"},
                    },
                    "required": ["input_text"],
                },
            ),
            types.Tool(
                name="toning-agent",
                description="Tone the given blog content to match Rapid Innovation's style.",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "input_text": {"type": "string"},
                    },
                    "required": ["input_text"],
                },
            ),
            types.Tool(
                name="format-blog-content",
                description="Format blog content with proper headings.",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "input_text": {"type": "string"},
                    },
                    "required": ["input_text"],
                },
            ),
            types.Tool(
                name="format-bullet-points",
                description="Format bullet points in the given content.",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "input_text": {"type": "string"},
                    },
                    "required": ["input_text"],
                },
            ),
        ]
        logging.info(f"handle_list_tools: tools={tools}")
        return tools
    except Exception as e:
        logging.exception("handle_list_tools: exception")
        raise
    finally:
        logging.info("handle_list_tools: end")


@server.call_tool()
async def handle_call_tool(
    name: str, arguments: dict | None
) -> list[types.TextContent | types.ImageContent | types.EmbeddedResource]:
    """
    Handle tool execution requests.
    Tools can modify server state and notify clients of changes.
    """
    logging.info(f"handle_call_tool: start, name={name}, arguments={arguments}")
    try:
        if name == "content-informative":
            if not arguments or "input_text" not in arguments:
                raise ValueError("Missing arguments or input_text")
            input_text = arguments["input_text"]
            logging.info(f"handle_call_tool: calling content_informative with input_text={input_text}")
            response = await content_informative(input_text)
            logging.info(f"handle_call_tool: content_informative response={response}")
            return [types.TextContent(type="text", text=response)]

        elif name == "content-technical":
            if not arguments or "input_text" not in arguments:
                raise ValueError("Missing arguments or input_text")
            input_text = arguments["input_text"]
            logging.info(f"handle_call_tool: calling content_technical with input_text={input_text}")
            response = await content_technical(input_text)
            logging.info(f"handle_call_tool: content_technical response={response}")
            return [types.TextContent(type="text", text=response)]

        elif name == "toning-agent":
            if not arguments or "input_text" not in arguments:
                raise ValueError("Missing arguments or input_text")
            input_text = arguments["input_text"]
            response = await toning_agent(input_text)
            return [types.TextContent(type="text", text=response)]

        elif name == "format-blog-content":
            if not arguments or "input_text" not in arguments:
                raise ValueError("Missing arguments or input_text")
            input_text = arguments["input_text"]
            response = await format_blog_content(input_text)
            return [types.TextContent(type="text", text=response)]

        elif name == "format-bullet-points":
            if not arguments or "input_text" not in arguments:
                raise ValueError("Missing arguments or input_text")
            input_text = arguments["input_text"]
            response = await format_bullet_points(input_text)
            return [types.TextContent(type="text", text=response)]

        else:
            raise ValueError(f"Unknown tool: {name}")

    except Exception as e:
        logging.exception("handle_call_tool: exception")
        raise
    finally:
        logging.info("handle_call_tool: end")


async def main():
    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="Main-content-generation",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )
