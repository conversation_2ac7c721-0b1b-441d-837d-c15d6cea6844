import os
import logging
from datetime import datetime
from decimal import Decimal

# Get current date and time
current_date = datetime.now().strftime('%Y-%m-%d')
current_time = datetime.now().strftime('%H-%M-%S')

# Define log directory structure
logs_dir = os.path.join(os.getcwd(), "logs", current_date)
os.makedirs(logs_dir, exist_ok=True)

# General logging setup
LOG_FILE_PATH = os.path.join(logs_dir, f"{current_time}.log")

logging.basicConfig(
    filename=LOG_FILE_PATH,
    format='[%(asctime)s] %(lineno)d %(name)s - %(levelname)s %(message)s',
    level=logging.INFO,
)

# Cost logging setup (separate configuration)
cost_logs_dir = os.path.join(os.getcwd(), "Cost_log", current_date)
os.makedirs(cost_logs_dir, exist_ok=True)

COST_LOG_FILE_PATH = os.path.join(cost_logs_dir, f"{current_time}.log")

# Create a separate logger for cost logs
cost_logger = logging.getLogger('cost_logger')
cost_handler = logging.FileHandler(COST_LOG_FILE_PATH)
cost_formatter = logging.Formatter('[%(asctime)s] %(lineno)d %(name)s - %(levelname)s %(message)s')
cost_handler.setFormatter(cost_formatter)
cost_logger.addHandler(cost_handler)
cost_logger.setLevel(logging.INFO)

# New cost log function for better understanding
def log_cost(cost_data, system_name="Webflow Helping Agents"):
    cost_including_cached = cost_data.get('usage_including_cached_inference', {}).get('total_cost', 'N/A')
    cost_excluding_cached = cost_data.get('usage_excluding_cached_inference', {}).get('total_cost', 'N/A')
    total_cost = cost_data.get('total_cost', 'N/A')

    # Convert to Decimal and format without scientific notation
    if cost_including_cached != 'N/A':
        cost_including_cached = f"{Decimal(cost_including_cached):f}"

    if cost_excluding_cached != 'N/A':
        cost_excluding_cached = f"{Decimal(cost_excluding_cached):f}"

    if total_cost != 'N/A':
        total_cost = f"{Decimal(total_cost):f}"

    # Log the cost information using the cost logger with system name included
    cost_logger.info(f'{system_name} Cost including cached inference: {cost_including_cached} USD')
    cost_logger.info(f'{system_name} Cost excluding cached inference: {cost_excluding_cached} USD')
    cost_logger.info(f'{system_name} Total cost: {total_cost} USD')
