import asyncio
import uuid
from datetime import datetime

from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
from pydantic import AnyUrl
import mcp.server.stdio
from .logger import logging
from .tools.subtopic_writer import content_informative, content_technical  ,  toning_agent, format_blog_content, format_bullet_points

from mcp.server.sse import SseServerTransport # Import SseServerTransport
from starlette.applications import Starlette # Import Starlette and Route for routes
from starlette.routing import Route
from starlette.responses import JSONResponse # Import JSONResponse for session response


class ContentGenerationServer:
    def __init__(self):
        logging.info("Initializing ContentGenerationServer")
        self.app = Server("Main-content-generation")
        self.sse = SseServerTransport("/analysis")
        self.session_store = {} # Initialize session store
        self.setup_tools()
        logging.info("ContentGenerationServer initialized")

    def setup_tools(self):
        logging.info("Setting up tools")

        @self.app.list_resources()
        async def handle_list_resources() -> list[types.Resource]:
            """
            List available note resources.
            Each note is exposed as a resource with a custom note:// URI scheme.
            """
            logging.info("handle_list_resources: start")
            try:
                resources = [
                    types.Resource(
                        uri=AnyUrl(f"note://internal/{name}"),
                        name=f"Note: {name}",
                        description=f"A simple note named {name}",
                        mimeType="text/plain",
                    )
                    for name in self.session_store # using session_store as notes for now
                ]
                logging.info(f"handle_list_resources: resources={resources}")
                return resources
            except Exception as e:
                logging.exception("handle_list_resources: exception")
                raise
            finally:
                logging.info("handle_list_resources: end")


        @self.app.read_resource()
        async def handle_read_resource(uri: AnyUrl) -> str:
            """
            Read a specific note's content by its URI.
            The note name is extracted from the URI host component.
            """
            logging.info(f"handle_read_resource: start, uri={uri}")
            try:
                if uri.scheme != "note":
                    raise ValueError(f"Unsupported URI scheme: {uri.scheme}")

                name = uri.path
                if name is not None:
                    name = name.lstrip("/")
                    content = self.session_store[name] # using session_store as notes for now
                    logging.info(f"handle_read_resource: content found for name={name}")
                    return content
                raise ValueError(f"Note not found: {name}")
            except Exception as e:
                logging.exception("handle_read_resource: exception")
                raise
            finally:
                logging.info("handle_read_resource: end")


        @self.app.list_prompts()
        async def handle_list_prompts() -> list[types.Prompt]:
            """
            List available prompts.
            Each prompt can have optional arguments to customize its behavior.
            """
            logging.info("handle_list_prompts: start")
            try:
                prompts = [
                    types.Prompt(
                        name="summarize-notes",
                        description="Creates a summary of all notes",
                        arguments=[
                            types.PromptArgument(
                                name="style",
                                description="Style of the summary (brief/detailed)",
                                required=False,
                            )
                        ],
                    )
                ]
                logging.info(f"handle_list_prompts: prompts={prompts}")
                return prompts
            except Exception as e:
                logging.exception("handle_list_prompts: exception")
                raise
            finally:
                logging.info("handle_list_prompts: end")


        @self.app.get_prompt()
        async def handle_get_prompt(
            name: str, arguments: dict[str, str] | None
        ) -> types.GetPromptResult:
            """
            Generate a prompt by combining arguments with server state.
            The prompt includes all current notes and can be customized via arguments.
            """
            logging.info(f"handle_get_prompt: start, name={name}, arguments={arguments}")
            try:
                if name != "summarize-notes":
                    raise ValueError(f"Unknown prompt: {name}")

                style = (arguments or {}).get("style", "brief")
                detail_prompt = " Give extensive details." if style == "detailed" else ""

                result = types.GetPromptResult(
                    description="Summarize the current notes",
                    messages=[
                        types.PromptMessage(
                            role="user",
                            content=types.TextContent(
                                type="text",
                                text=f"Here are the current notes to summarize:{detail_prompt}\n\n"
                                + "\n".join(
                                    f"- {name}: {content}"
                                    for name, content in self.session_store.items() # using session_store as notes
                                ),
                            ),
                        )
                    ],
                )
                logging.info(f"handle_get_prompt: result={result}")
                return result
            except Exception as e:
                logging.exception("handle_get_prompt: exception")
                raise
            finally:
                logging.info("handle_get_prompt: end")


        @self.app.list_tools()
        async def handle_list_tools() -> list[types.Tool]:
            """
            List available tools.
            Each tool specifies its arguments using JSON Schema validation.
            """
            logging.info("handle_list_tools: start")
            try:
                tools = [
                    types.Tool(
                        name="content-informative",
                        description="Generate informative content from input text.",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "input_text": {"type": "string"},
                            },
                            "required": ["input_text"],
                        },
                    ),
                    types.Tool(
                        name="content-technical",
                        description="Generate technical content from input text. Pass only points no Ebaloration is required , only use points to generate content",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "input_text": {"type": "string"},
                            },
                            "required": ["input_text"],
                        },
                    ),
                    types.Tool(
                        name="toning-agent",
                        description="Tone the given blog content to match Rapid Innovation's style.",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "input_text": {"type": "string"},
                            },
                            "required": ["input_text"],
                        },
                    ),
                    types.Tool(
                        name="format-blog-content",
                        description="Format blog content with proper headings.",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "input_text": {"type": "string"},
                            },
                            "required": ["input_text"],
                        },
                    ),
                    types.Tool(
                        name="format-bullet-points",
                        description="Format bullet points in the given content.",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "input_text": {"type": "string"},
                            },
                            "required": ["input_text"],
                        },
                    ),
                ]
                logging.info(f"handle_list_tools: tools={tools}")
                return tools
            except Exception as e:
                logging.exception("handle_list_tools: exception")
                raise
            finally:
                logging.info("handle_list_tools: end")


        @self.app.call_tool()
        async def handle_call_tool(
            name: str, arguments: dict | None
        ) -> list[types.TextContent | types.ImageContent | types.EmbeddedResource]:
            """
            Handle tool execution requests.
            Tools can modify server state and notify clients of changes.
            """
            logging.info(f"handle_call_tool: start, name={name}, arguments={arguments}")
            try:
                if name == "content-informative":
                    if not arguments or "input_text" not in arguments:
                        raise ValueError("Missing arguments or input_text")
                    input_text = arguments["input_text"]
                    logging.info(f"handle_call_tool: calling content_informative with input_text={input_text}")
                    response = await content_informative(input_text)
                    logging.info(f"handle_call_tool: content_informative response={response}")
                    return [types.TextContent(type="text", text=response)]

                elif name == "content-technical":
                    if not arguments or "input_text" not in arguments:
                        raise ValueError("Missing arguments or input_text")
                    input_text = arguments["input_text"]
                    logging.info(f"handle_call_tool: calling content_technical with input_text={input_text}")
                    response = await content_technical(input_text)
                    logging.info(f"handle_call_tool: content_technical response={response}")
                    return [types.TextContent(type="text", text=response)]

                elif name == "toning-agent":
                    if not arguments or "input_text" not in arguments:
                        raise ValueError("Missing arguments or input_text")
                    input_text = arguments["input_text"]
                    response = await toning_agent(input_text)
                    return [types.TextContent(type="text", text=response)]

                elif name == "format-blog-content":
                    if not arguments or "input_text" not in arguments:
                        raise ValueError("Missing arguments or input_text")
                    input_text = arguments["input_text"]
                    response = await format_blog_content(input_text)
                    return [types.TextContent(type="text", text=response)]

                elif name == "format-bullet-points":
                    if not arguments or "input_text" not in arguments:
                        raise ValueError("Missing arguments or input_text")
                    input_text = arguments["input_text"]
                    response = await format_bullet_points(input_text)
                    return [types.TextContent(type="text", text=response)]

                else:
                    raise ValueError(f"Unknown tool: {name}")

            except Exception as e:
                logging.exception("handle_call_tool: exception")
                raise
            finally:
                logging.info("handle_call_tool: end")

    async def generate_session(self, request):
        logging.info("generate_session: start")
        session_id = str(uuid.uuid4())
        self.session_store[session_id] = {"client": request.client.host, "created_at": datetime.now()}
        logging.info(f"Session created: {session_id} for {request.client.host}")
        return JSONResponse({"session_id": session_id})

    class HandleSSE:
        def __init__(self, sse, main_server): # Pass main_server instance
            self.sse = sse
            self.main_server = main_server # Store main server instance
            logging.info("HandleSSE.__init__")

        async def __call__(self, scope, receive, send):
            logging.info("HandleSSE.__call__: start")
            try:
                async with self.sse.connect_sse(scope, receive, send) as streams:
                    await self.main_server.app.run( # Use stored main server instance
                        streams[0],
                        streams[1],
                        InitializationOptions( # Initialization options remain the same
                            server_name="Main-content-generation",
                            server_version="0.1.0",
                            capabilities=self.main_server.app.get_capabilities(
                                notification_options=NotificationOptions(),
                                experimental_capabilities={},
                            ),
                        )
                    )
            except Exception as e:
                logging.error(f"HandleSSE.__call__: SSE connection error: {str(e)}")
                raise
            finally:
                logging.info("HandleSSE.__call__: end")


    class HandleMessages:
        def __init__(self, sse):
            self.sse = sse
            logging.info("HandleMessages.__init__")

        async def __call__(self, scope, receive, send):
            logging.info("HandleMessages.__call__: start")
            await self.sse.handle_post_message(scope, receive, send)
            logging.info("HandleMessages.__call__: end")


    def create_app(self):
        logging.info("create_app: start")
        routes = [
            Route("/session", endpoint=self.generate_session, methods=["GET"]),
            Route("/sse", endpoint=ContentGenerationServer.HandleSSE(self.sse, self), methods=["GET"]),
            Route("/analysis", endpoint=ContentGenerationServer.HandleMessages(self.sse), methods=["POST"]),
        ]
        app = Starlette(debug=True, routes=routes) # Create Starlette app instance
        logging.info("create_app: end")
        return app


async def main():
    logging.info("main: start")
    content_server = ContentGenerationServer()
    app = content_server.create_app()
    logging.info("main: application created")
    # No longer using stdio_server directly, app is run via uvicorn or similar ASGI server.
    # The SSE and HTTP routes are handled by the Starlette application.
    logging.info("main: end")
    return app # return app for uvicorn


import uvicorn
def run_main():
    logging.info("__main__: start")
    app = asyncio.run(main()) # create and get app instance
    logging.info("__main__: running uvicorn server")
    uvicorn.run(app, host="127.0.0.1", port=5003)
    logging.info("__main__: end")
