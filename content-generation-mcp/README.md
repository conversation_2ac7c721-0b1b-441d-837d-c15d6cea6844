# Main-content-generation MCP server
This MCP server helps to generate and format blog content with various styles and formats.

## Components

### Resources
The server implements a simple note storage system with:
- Custom note:// URI scheme for accessing individual notes
- Each note resource has a name, description, and text/plain mimetype

### Prompts
The server provides a single prompt:
- summarize-notes: Creates summaries of all stored notes
  - Optional "style" argument to control detail level (brief/detailed)
  - Generates prompt combining all current notes with style preference

### Tools
The server implements the following tools:
- content-informative: Generates informative content from input text
  - Takes "input_text" as a required string argument
  - Updates server state and notifies clients of resource changes
- content-technical: Generates technical content from input text
  - Takes "input_text" as a required string argument
  - Updates server state and notifies clients of resource changes
- toning-agent: Tones the given blog content to match Rapid Innovation's style
  - Takes "input_text" as a required string argument
  - Updates server state and notifies clients of resource changes
- format-blog-content: Formats blog content with proper headings
  - Takes "input_text" as a required string argument
  - Updates server state and notifies clients of resource changes
- format-bullet-points: Formats bullet points in the given content
  - Takes "input_text" as a required string argument
  - Updates server state and notifies clients of resource changes

## Quickstart

### Install

```bash
git clone https://gitlab.rapidinnovation.tech/mcp-server/content-generation-mcp.git
cd content-generation-mcp
```

#### Create a virtual environment:

```bash
python -m venv venv
source venv/bin/activate 
```

```bash
uv pip install .
```

### Run
```bash
uv run main-content-generation
```

Or directly with Python:
```bash 
python server.py
``` 



## Development

### Building and Publishing

#### To prepare the package for distribution:
#### Sync dependencies and update lockfile:

```bash
uv sync
```
#### Build package distributions:
```bash
uv build
```
This will create source and wheel distributions in the dist/ directory.

### Publish to PyPI:
```bash
uv publish
```
Note: You'll need to set PyPI credentials via environment variables or command flags:

Token: --token or UV_PUBLISH_TOKEN
Or username/password: --username/UV_PUBLISH_USERNAME and --password/UV_PUBLISH_PASSWORD


### Debugging
You can use the MCP inspector to debug the server. For uvx installations:
```bash
npx @modelcontextprotocol/inspector uvx main-content-generation
```
Or if you've installed the package in a specific directory or are developing on it:
```bash
cd /path/to/your/content-generation-mcp
```
```
npx @modelcontextprotocol/inspector uv run main-content-generation
```
Running tail -n 20 -f ~/Library/Logs/Claude/mcp*.log will show the logs from the server and may help you debug any issues.

### Using Uvicorn for Development

For direct debugging with Uvicorn:
```bash
npx @modelcontextprotocol/inspector uvicorn src.main_content_generation.server:app --reload
```
Upon launching, the Inspector will display a URL that you can access in your browser to begin debugging.
### Using Docker
#### Build
#### Docker build:
```bash 
docker build -t main-content-generation .
```
#### Run

Docker run:
```bash
docker run -it main-content-generation
```