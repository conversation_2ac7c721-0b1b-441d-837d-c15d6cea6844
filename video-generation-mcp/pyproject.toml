[project]
name = "video-generation-mcp"
version = "0.1.0"
description = "This mcp server help to generate the final video using moviepy and ffmpeg"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "boto3>=1.37.11",
    "ffmpeg-python>=0.2.0",
    "imagemagic>=0.2.1",
    "mcp>=1.9.4",
    "mcp-proxy>=0.7.0",
    "moviepy==1.0.3",
    "opencv-python>=*********",
    "pip>=25.0.1",
    "pysrt>=1.1.2",
    "pytest>=8.3.5",
    "requests>=2.32.3",
    "scipy>=1.15.2",
    "srt-equalizer>=0.1.10",
]
[[project.authors]]
name = "<PERSON><PERSON><PERSON>"
email = "<EMAIL>"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
video-generation-mcp = "video_generation_mcp:main"
