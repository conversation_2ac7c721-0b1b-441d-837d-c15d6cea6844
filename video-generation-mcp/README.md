# ******************** MCP server

This MCP server helps to generate the final video using moviepy and ffmpeg.

## Project Structure

```
********************/
├── Dockerfile              # Docker configuration file
├── pyproject.toml         # Python project metadata and dependencies
├── uv.lock                # UV dependency lock file
├── README.md             # Project documentation              # Directory for generated videos
├── temp/                 # Temporary files directory
└── src/video_generation_mcp/ # Main package directory
    ├── __init__.py
    ├── cli/             # Command-line interface modules
    │   └── __init__.py
    ├── config/          # Configuration files and settings
    │   └── __init__.py
    ├── core/            # Core functionality
    │   └── __init__.py
    ├── services/        # Service layer modules
    │   ├── __init__.py
    │   └── video.py    # Video processing service
    └── utils/           # Utility functions and helpers
        └── __init__.py
```

## Components

### Resources

The server implements a simple note storage system with:

- Custom note:// URI scheme for accessing individual notes
- Each note resource has a name, description, and text/plain mimetype

### Prompts

The server provides a single prompt:

- summarize-notes: Creates summaries of all stored notes
  - Optional "style" argument to control detail level (brief/detailed)
  - Generates prompt combining all current notes with style preference

### Tools

The server implements one tool:

- add-note: Adds a new note to the server
  - Takes "name" and "content" as required string arguments
  - Updates server state and notifies clients of resource changes

## Configuration

### Usage with Claude Desktop

Add this to your `claude_desktop_config.json`:

<details>
<summary>Using uvx</summary>

```json
"mcpServers": {
  "********************": {
    "command": "uvx",
    "args": ["********************"]
  }
}
```

</details>

<details>
<summary>Using docker</summary>

- Note: replace '/Users/<USER>' with the path that you want to be accessible by this tool

```json
"mcpServers": {
  "********************": {
    "command": "docker",
    "args": ["run", "--rm", "-i", "--mount", "type=bind,src=/Users/<USER>/Users/<USER>", "********************"]
  }
}
```

</details>

<details>
<summary>Using pip installation</summary>

```json
"mcpServers": {
  "********************": {
    "command": "python",
    "args": ["-m", "video_generation_mcp"]
  }
}
```

</details>

## Quickstart

### Install

1. Clone the repository:

```bash
git clone https://gitlab.rapidinnovation.tech/mcp-server/********************.git
cd ********************
```

2. Create a virtual environment:

```bash
python -m venv .venv
source .venv/bin/activate  # On Windows use `.venv\Scripts\activate`
```

3. Create a `.env` file using the `.env.example` file:

```bash
cp .env.example .env
```

4. Run the server using `uv` command:

```bash
uv run ********************
```

This process will install all dependencies.

### Claude Desktop

On MacOS: `~/Library/Application\ Support/Claude/claude_desktop_config.json`
On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

<details>
  <summary>Development/Unpublished Servers Configuration</summary>
  ```
  "mcpServers": {
    "********************": {
      "command": "uv",
      "args": [
        "--directory",
        "/Users/<USER>/Desktop/Projects/mcp-server/********************",
        "run",
        "********************"
      ]
    }
  }
  ```
</details>

<details>
  <summary>Published Servers Configuration</summary>
  ```
  "mcpServers": {
    "********************": {
      "command": "uvx",
      "args": [
        "********************"
      ]
    }
  }
  ```
</details>

## Development

### Building and Publishing

To prepare the package for distribution:

1. Sync dependencies and update lockfile:

```bash
uv sync
```

2. Build package distributions:

```bash
uv build
```

This will create source and wheel distributions in the `dist/` directory.

3. Publish to PyPI:

```bash
uv publish
```

Note: You'll need to set PyPI credentials via environment variables or command flags:

- Token: `--token` or `UV_PUBLISH_TOKEN`
- Or username/password: `--username`/`UV_PUBLISH_USERNAME` and `--password`/`UV_PUBLISH_PASSWORD`

### Debugging

You can use the MCP inspector to debug the server. For uvx installations:

```bash
npx @modelcontextprotocol/inspector uvx ********************
```

Or if you've installed the package in a specific directory or are developing on it:

```bash
cd /Users/<USER>/Desktop/Projects/mcp-server/********************
npx @modelcontextprotocol/inspector uv run ********************
```

Running `tail -n 20 -f ~/Library/Logs/Claude/mcp*.log` will show the logs from the server and may help you debug any issues.

### Using Docker

#### Build

Docker build:

```bash
docker build -t ******************** .
```

#### Run

Docker run:

```bash
docker run -it ********************

docker run -p 5006:5006 -it --env-file .env ********************
```
