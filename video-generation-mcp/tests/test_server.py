import pytest
import asyncio
from video_generation_mcp.services.video import VideoService


# Fixture to create a temporary git repository for testing
@pytest.fixture
def test(input):
    pass


# Additional tests for MCP server functionality can be added here
def test_mcp_server_functionality():
    # Add your MCP server test code here
    pass


async def test_video_generation():
    res = await VideoService().process_video(
        {
            "session_id": "test",
            "view_type": "LANDSCAPE",
            "stock_video_clips": [
                {
                    "at_time": 0,
                    "url": "https://videos.pexels.com/video-files/8328042/8328042-uhd_3840_2160_25fps.mp4",
                },
                {
                    "at_time": 8,
                    "url": "https://videos.pexels.com/video-files/18069166/18069166-uhd_3840_2160_24fps.mp4",
                },
                {
                    "at_time": 34,
                    "url": "https://videos.pexels.com/video-files/15940565/15940565-hd_1920_1080_60fps.mp4",
                },
                {
                    "at_time": 59,
                    "url": "https://videos.pexels.com/video-files/8566712/8566712-uhd_3840_2160_30fps.mp4",
                },
                {
                    "at_time": 75,
                    "url": "https://videos.pexels.com/video-files/4169331/4169331-hd_1280_720_30fps.mp4",
                },
            ],
            "stock_image_clips": [
                {
                    "at_time": 0.0,
                    "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/d7ef8aaf-d390-44de-80a3-0ca27a8928c1.jpg",
                },
                {
                    "at_time": 7.0,
                    "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/6a044045-fc3c-4ac6-bdb6-751d64893fe2.jpg",
                },
                {
                    "at_time": 34.0,
                    "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/1ae5deac-f625-4ee2-ad4f-fc28e16aa62e.jpg",
                },
                {
                    "at_time": 43.0,
                    "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/bf9de270-0dbd-4d51-9bf5-d009870ad82f.jpg",
                },
                {
                    "at_time": 63.0,
                    "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/29557107-99f3-43a5-b252-c97bd6a875c6.jpg",
                },
                {
                    "at_time": 79.0,
                    "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/7c49c060-5fc3-41c0-b258-62d574030719.jpg",
                },
                {
                    "at_time": 86.0,
                    "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/c87b93ee-86b1-475e-bda4-711cd0e8a36f.jpg",
                },
            ],
            "event_stock_clips": [],
            "audio_urls": [
                "https://ciny-dev.s3.amazonaws.com/ciny-dev/audio/7951251e-c498-4f06-a5c8-8d1aafebb04f.mp3",
                "https://ciny-dev.s3.amazonaws.com/ciny-dev/audio/c76d3aa3-3b2d-41ff-a3c6-9d19b872c6a3.mp3",
                "https://ciny-dev.s3.amazonaws.com/ciny-dev/audio/14f35194-bae2-46fd-b7bb-4179684e73b5.mp3",
                "https://ciny-dev.s3.amazonaws.com/ciny-dev/audio/c50685af-c9c1-4a3b-9e33-d5158b6908f8.mp3",
                "https://ciny-dev.s3.amazonaws.com/ciny-dev/audio/594ce07c-8b06-4e26-9c86-6a1f4b08c4a1.mp3",
                "https://ciny-dev.s3.amazonaws.com/ciny-dev/audio/256c4baf-1d11-400f-ae8a-616a01380098.mp3",
                "https://ciny-dev.s3.amazonaws.com/ciny-dev/audio/a8727751-9dd9-4dfb-ba40-6b582566cbc5.mp3",
            ],
            "avatar_video_urls": None,
            "caption": {
                "color_code": "#FFFF00",
                "font_path": "http://fonts.gstatic.com/s/rubik/v3/4sMyW_teKWHB3K8Hm-Il6A.ttf",
            },
            "subtitles": "1\n00:00:00,160 --> 00:00:03,784\nWhoa. In just 30 seconds, we're taking a quick dive into the world of AI\n\n2\n00:00:03,832 --> 00:00:07,560\nautomation. Technology meets everyday convenience.\n\n3\n00:00:07,720 --> 00:00:11,416\nSounds wild, right? Ever wondered how AI reshapes\n\n4\n00:00:11,448 --> 00:00:15,016\nour daily routines right from our smartphones? Well,\n\n5\n00:00:15,088 --> 00:00:18,488\nstick around. AI automation, it's like a game\n\n6\n00:00:18,544 --> 00:00:22,152\nchanger for how tasks get done. Why all the buzz?\n\n7\n00:00:22,296 --> 00:00:25,576\nBecause it combines artificial intelligence, those awesome machine\n\n8\n00:00:25,608 --> 00:00:29,084\nlearnings, algorithms to tackle repetitive tasks. So you\n\n9\n00:00:29,092 --> 00:00:33,240\ncan spend more time being creative or you know, just more you.\n\n10\n00:00:33,540 --> 00:00:37,228\nHere are some quick Efficiency boost\n\n11\n00:00:37,404 --> 00:00:41,920\nAI can significantly improve business productivity\n\n12\n00:00:42,340 --> 00:00:45,612\nCost savings. Implementing AI solutions can\n\n13\n00:00:45,636 --> 00:00:50,080\nlead to notable reductions in operational costs versatility.\n\n14\n00:00:50,500 --> 00:00:53,922\nWhether it's chatbots or data entry, AI's got you\n\n15\n00:00:53,946 --> 00:00:57,910\ncovered, saving both time and resources.\n\n16\n00:00:58,650 --> 00:01:02,498\nFrom my point of view, AI isn't just about cranking up efficiency.\n\n17\n00:01:02,594 --> 00:01:07,154\nIt's paving the way for innovation. Can you imagine tasks\n\n18\n00:01:07,202 --> 00:01:11,042\nthat used to hog hours are now done in just seconds? That's not\n\n19\n00:01:11,146 --> 00:01:14,114\njust handy, it's the future knocking on your door.\n\n20\n00:01:14,242 --> 00:01:17,922\nIf any of this has you nodding along, go ahead and hit that thumbs up.\n\n21\n00:01:18,026 --> 00:01:21,504\nAnd hey, subscribe for more tech tidbits that'll keep you ahead of\n\n22\n00:01:21,512 --> 00:01:25,008\nthe curve. Blown away? Drop a comment if you're ready\n\n23\n00:01:25,024 --> 00:01:28,480\nto step up your game. Visuals and cues make\n\n24\n00:01:28,520 --> 00:01:32,192\nthings stick, right? Let's keep reshaping our tech driven\n\n25\n00:01:32,256 --> 00:01:33,720\nworld one step at a time.",
            # "subtitles": None,
            "intro_video": {
                "url": "https://videos.pexels.com/video-files/5509552/5509552-uhd_3840_2160_30fps.mp4",
                "mime_type": "image/jpeg",
            },
            "outro_video": {
                "url": "https://videos.pexels.com/video-files/5509552/5509552-uhd_3840_2160_30fps.mp4",
                "mime_type": "image/jpeg",
            },
            "logo": {
                "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/ce48966f-deb3-4909-b3e2-4122727fdeb6.jpg",
                "mime_type": "image/jpeg",
            },
            "music": {
                "url": "https://peregrine-results.s3.amazonaws.com/e1ifRoSDEhUlEJmNi5.mp3",
                "mime_type": "audio/mpeg",
            },
        },
    )

    print("response", res)


if __name__ == "__main__":
    asyncio.run(test_video_generation())
