# Use a Python image with uv pre-installed
FROM ghcr.io/astral-sh/uv:python3.12-bookworm-slim AS uv

# Install the project into `/app`
WORKDIR /app

# Enable bytecode compilation
ENV UV_COMPILE_BYTECODE=1

# Copy from the cache instead of linking since it's a mounted volume
ENV UV_LINK_MODE=copy
# Install project dependencies using the lockfile
RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv sync --frozen --no-install-project --no-dev --no-editable

# Copy the rest of the project source code and install it
ADD . /app
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-dev --no-editable

# Ensure /root/.local exists
RUN mkdir -p /root/.local

# Stage 2: Final image with runtime dependencies
FROM python:3.12-slim-bookworm

# Install system dependencies for OpenCV, ImageMagick, and other tools
RUN apt-get update && apt-get install -y --no-install-recommends \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    imagemagick \
    gcc \
    ffmpeg \
    libmagick++-dev \
    && rm -rf /var/lib/apt/lists/*

# Update ImageMagick policy to allow necessary operations
RUN sed -i 's/rights="none" pattern="@\*"/rights="read|write" pattern="@*"/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/<policy domain="coder" rights="none" pattern="VIDEO" \/>/<policy domain="coder" rights="read|write" pattern="VIDEO" \/>/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/<policy domain="coder" rights="none" pattern="FILE" \/>/<policy domain="coder" rights="read|write" pattern="FILE" \/>/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/rights="none" pattern="font"/rights="read" pattern="font"/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/domain="resource" name="memory" value="256MiB"/domain="resource" name="memory" value="1GiB"/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/domain="resource" name="map" value="512MiB"/domain="resource" name="map" value="1GiB"/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/domain="resource" name="width" value="16KP"/domain="resource" name="width" value="64KP"/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/domain="resource" name="height" value="16KP"/domain="resource" name="height" value="64KP"/' /etc/ImageMagick-6/policy.xml && \
    sed -i 's/domain="coder" rights="none" pattern="http"/domain="coder" rights="read" pattern="http"/' /etc/ImageMagick-6/policy.xml

# Set environment variables
ENV IMAGEMAGICK_BINARY=/usr/bin/convert \
    PYTHONUNBUFFERED=1 \
    VIDEO_TEMP_DIR=/app/temp \
    PATH="/app/.venv/bin:$PATH"

WORKDIR /app

# Copy virtual environment and application files
COPY --from=uv /root/.local /root/.local
COPY --from=uv --chown=root:root /app/.venv /app/.venv
COPY --from=uv /app /app

# Create temp directory with proper permissions
RUN mkdir -p /app/temp && \
    chmod -R 777 /app/temp && \
    chown -R root:root /app/temp

# Create volume for temp directory
VOLUME ["/app/temp"]

# Set the default entrypoint
ENTRYPOINT ["video-generation-mcp"]
