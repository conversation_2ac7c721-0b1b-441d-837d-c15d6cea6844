import os
import requests
import srt_equalizer
from ..helper.utils import is_valid_url
import urllib.request
from moviepy.editor import AudioFileClip, concatenate_audioclips


from concurrent.futures import ThreadPoolExecutor, as_completed


def save_audio(video_id, audio_links, max_workers=4):
    try:
        tts_path = f"./temp/{video_id}/audio.mp3"
        audio_clips = []

        def download_audio(index, audio_url):
            if is_valid_url(audio_url):
                # Download the audio files
                file_path = os.path.join(
                    os.getcwd(),
                    f"./temp/{video_id}/temp_audio{index}.mp3",
                )
                urllib.request.urlretrieve(audio_url, file_path)
            else:
                file_path = f"./temp/{video_id}/{audio_url}"
            return AudioFileClip(file_path)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [
                executor.submit(download_audio, index, audio_url)
                for index, audio_url in enumerate(audio_links)
            ]
            for future in as_completed(futures):
                audio_clips.append(future.result())

        audio_clip = concatenate_audioclips(audio_clips)

        if not os.path.exists(tts_path):
            # Write the combined audio to the output file
            audio_clip.write_audiofile(tts_path)

        return True

    except Exception as err:
        print(f"[-] Error generate_subtitles: {str(err)}")
        raise


def download_audio(url, output_filename=None):
    """
    Downloads audio from a URL and saves it to the specified output filename.

    Args:
        url (str): The URL of the audio file to download
        output_filename (str, optional): The filename to save the audio as.
            If not provided, extracts filename from URL.

    Returns:
        str: Path to the downloaded file on success, None on failure
    """
    try:
        # Send a GET request to the URL
        response = requests.get(url, stream=True)
        response.raise_for_status()  # Raise an exception for HTTP errors

        # If output filename not provided, extract from URL
        if not output_filename:
            output_filename = os.path.basename(url).split("?")[
                0
            ]  # Remove query params if any

            # If URL doesn't have a proper filename, use default
            if not output_filename or "." not in output_filename:
                # Try to get content type from headers
                content_type = response.headers.get("Content-Type", "")
                if "mp3" in content_type:
                    output_filename = "downloaded_audio.mp3"
                elif "wav" in content_type:
                    output_filename = "downloaded_audio.wav"
                elif "ogg" in content_type:
                    output_filename = "downloaded_audio.ogg"
                elif "audio" in content_type:
                    output_filename = "downloaded_audio.mp3"  # Default to mp3
                else:
                    output_filename = "downloaded_audio"

        # Download and save the file
        with open(output_filename, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        print(f"Audio successfully downloaded to {output_filename}")
        return output_filename

    except requests.exceptions.RequestException as e:
        print(f"Error downloading audio: {e}")
        return None
    except IOError as e:
        print(f"Error saving file: {e}")
        return None


def create_subtitle_file(video_id, subtitle):

    # Save subtitles
    subtitles_path = f"./temp/{video_id}/subtitle.srt"

    if not os.path.exists(subtitles_path):

        def equalize_subtitles(srt_path: str, max_chars: int = 10) -> None:
            # Equalize subtitles
            srt_equalizer.equalize_srt_file(srt_path, srt_path, max_chars)

        with open(subtitles_path, "w") as file:
            file.write(subtitle)

        # Equalize subtitles
        equalize_subtitles(subtitles_path)

    return subtitles_path
