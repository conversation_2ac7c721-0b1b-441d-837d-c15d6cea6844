import numpy as np
from functools import lru_cache
import cv2
import os
import sys
from moviepy.editor import TextClip

from ..helper.utils import download_font

# Add the parent directory to the system path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))


# Caching function
def cached_operation(func):
    @lru_cache(maxsize=None)
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)

    return wrapper


def add_zoom_effect(clip, start_time, end_time, zoom_type="in", zoom_factor=1.5):
    """
    Add a zoom in or zoom out effect to a video clip.

    Parameters:
    -----------
    clip : VideoClip
        The video clip to apply the zoom effect to
    start_time : float
        Time in seconds when the zoom effect should start
    end_time : float
        Time in seconds when the zoom effect should end
    zoom_type : str, optional
        Type of zoom: "in" to zoom in or "out" to zoom out (default: "in")
    zoom_factor : float, optional
        Maximum zoom factor (default: 1.5)
        For zoom in: starts at 1.0, ends at zoom_factor
        For zoom out: starts at zoom_factor, ends at 1.0

    Returns:
    --------
    VideoClip
        New video clip with the zoom effect applied
    """
    # Original clip dimensions
    w, h = clip.size

    # Validate inputs
    if start_time < 0 or end_time > clip.duration or start_time >= end_time:
        raise ValueError("Invalid time range")
    if zoom_type not in ["in", "out"]:
        raise ValueError("zoom_type must be either 'in' or 'out'")

    if zoom_factor <= 0:
        raise ValueError("zoom_factor must be positive")

    # Define the zoom function
    def zoom_effect(get_frame, t):
        # Get the current frame
        frame = get_frame(t)

        # If outside the zoom time range, return the original frame
        if t < start_time or t > end_time:
            return frame

        # Calculate current zoom progress (0 to 1)
        progress = (t - start_time) / (end_time - start_time)

        # Calculate current zoom scale based on zoom type
        if zoom_type == "in":
            # For zoom in: scale goes from 1.0 to zoom_factor
            scale = 1.0 + progress * (zoom_factor - 1.0)
        else:  # zoom out
            # For zoom out: scale goes from zoom_factor to 1.0
            scale = zoom_factor - progress * (zoom_factor - 1.0)

        # Calculate new dimensions
        new_w = int(w * scale)
        new_h = int(h * scale)

        resized_frame = cv2.resize(
            frame, (new_w, new_h), interpolation=cv2.INTER_LINEAR
        )

        # Calculate the center crop
        x_center = new_w // 2
        y_center = new_h // 2

        x1 = max(0, x_center - w // 2)
        y1 = max(0, y_center - h // 2)
        x2 = min(new_w, x_center + w // 2)
        y2 = min(new_h, y_center + h // 2)

        # Crop the frame to original dimensions
        cropped_frame = resized_frame[y1:y2, x1:x2]

        # Handle edge cases where the cropped frame might be smaller than original dimensions
        if cropped_frame.shape[0] < h or cropped_frame.shape[1] < w:
            result = np.zeros((h, w, 3), dtype=np.uint8)
            # Calculate the position to place the cropped frame
            y_offset = (h - cropped_frame.shape[0]) // 2
            x_offset = (w - cropped_frame.shape[1]) // 2
            result[
                y_offset : y_offset + cropped_frame.shape[0],
                x_offset : x_offset + cropped_frame.shape[1],
            ] = cropped_frame
            return result

        return cropped_frame

    # Apply the zoom effect
    zoomed_clip = clip.fl(lambda gf, t: zoom_effect(gf, t))

    return zoomed_clip


def crop_to_reel_video(clip):

    # Get the original video dimensions
    original_width, original_height = clip.size

    # Calculate the aspect ratio of the original video
    original_aspect_ratio = original_width / original_height

    # Define the desired aspect ratio for Shorts (9:16)
    shorts_aspect_ratio = 9 / 16

    # Determine if we need to crop horizontally or vertically
    if original_aspect_ratio > shorts_aspect_ratio:
        # Crop horizontally
        new_width = int(original_height * shorts_aspect_ratio)
        new_height = original_height
        x1 = (original_width - new_width) // 2
        x2 = x1 + new_width
        y1 = 0
        y2 = original_height
    else:
        # Crop vertically
        new_height = int(original_width / shorts_aspect_ratio)
        new_width = original_width
        x1 = 0
        x2 = original_width
        y1 = (original_height - new_height) // 2
        y2 = y1 + new_height

    # Crop the video
    clip = clip.crop(x1=x1, x2=x2, y1=y1, y2=y2)

    return clip


@cached_operation
def create_text_generator(
    font_path="./asset/font/bold_font.ttf", font_size=50, color="#FFFF00"
):

    if font_path != "./asset/font/bold_font.ttf":
        font_path = download_font(font_path)
    return lambda txt: TextClip(
        txt=txt.upper(),
        font=font_path,
        fontsize=font_size,
        color=color,
        stroke_color="Black",
        stroke_width=2,
        align="center",
        method="caption",
    )


# def add_zoom_effect(clip, start_time, end_time, zoom_type="in", zoom_factor=1.5):
#     """
#     Add a zoom in or zoom out effect to a clip.
#     Works with both VideoClip and ImageClip objects.

#     Parameters:
#     -----------
#     clip : VideoClip or ImageClip
#         The clip to apply the zoom effect to
#     start_time : float
#         Time in seconds when the zoom effect should start
#     end_time : float
#         Time in seconds when the zoom effect should end
#     zoom_type : str, optional
#         Type of zoom: "in" to zoom in or "out" to zoom out (default: "in")
#     zoom_factor : float, optional
#         Maximum zoom factor (default: 1.5)
#         For zoom in: starts at 1.0, ends at zoom_factor
#         For zoom out: starts at zoom_factor, ends at 1.0

#     Returns:
#     --------
#     VideoClip
#         New video clip with the zoom effect applied
#     """

#     # Original clip dimensions
#     w, h = clip.size

#     # Validate inputs
#     if start_time < 0 or end_time > clip.duration or start_time >= end_time:
#         raise ValueError("Invalid time range")

#     if zoom_type not in ["in", "out"]:
#         raise ValueError("zoom_type must be either 'in' or 'out'")

#     if zoom_factor <= 0:
#         raise ValueError("zoom_factor must be positive")

#     # Define the zoom function
#     def zoom_effect(t):
#         # Get the current frame
#         frame = clip.get_frame(t)

#         # If outside the zoom time range, return the original frame
#         if t < start_time or t > end_time:
#             return frame

#         # Calculate current zoom progress (0 to 1)
#         progress = (t - start_time) / (end_time - start_time)

#         # Calculate current zoom scale based on zoom type
#         if zoom_type == "in":
#             # For zoom in: scale goes from 1.0 to zoom_factor
#             scale = 1.0 + progress * (zoom_factor - 1.0)
#         else:  # zoom out
#             # For zoom out: scale goes from zoom_factor to 1.0
#             scale = zoom_factor - progress * (zoom_factor - 1.0)

#         # Calculate new dimensions
#         new_w = int(w * scale)
#         new_h = int(h * scale)

#         # Resize the frame
#         resized_frame = cv2.resize(
#             frame, (new_w, new_h), interpolation=cv2.INTER_LINEAR
#         )

#         # Calculate the center crop
#         x_center = new_w // 2
#         y_center = new_h // 2

#         x1 = max(0, x_center - w // 2)
#         y1 = max(0, y_center - h // 2)
#         x2 = min(new_w, x_center + w // 2)
#         y2 = min(new_h, y_center + h // 2)

#         # Crop the frame to original dimensions
#         cropped_frame = resized_frame[y1:y2, x1:x2]

#         # Handle edge cases where the cropped frame might be smaller than original dimensions
#         if cropped_frame.shape[0] < h or cropped_frame.shape[1] < w:
#             result = np.zeros((h, w, 3), dtype=np.uint8)
#             # Calculate the position to place the cropped frame
#             y_offset = (h - cropped_frame.shape[0]) // 2
#             x_offset = (w - cropped_frame.shape[1]) // 2
#             result[
#                 y_offset : y_offset + cropped_frame.shape[0],
#                 x_offset : x_offset + cropped_frame.shape[1],
#             ] = cropped_frame
#             return result

#         return cropped_frame

#     # Create a new VideoClip with the zoom effect
#     zoomed_clip = VideoClip(lambda t: zoom_effect(t), duration=clip.duration)

#     # Preserve audio if present
#     if hasattr(clip, "audio") and clip.audio is not None:
#         zoomed_clip.audio = clip.audio

#     return zoomed_clip
