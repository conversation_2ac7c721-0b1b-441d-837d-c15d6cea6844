import logging
import os
import uuid
from video_generation_mcp.services.audio import download_audio, create_subtitle_file

from video_generation_mcp.constants.enum import VideoViewType


from video_generation_mcp.constants.constant import MESSAGES, VIDEO_TEMPLATES

# from ..youtube import download_youtube_video

from video_generation_mcp.helper.utils import (
    save_video,
    download_image,
    clean_dir,
    format_duration,
    create_dir,
    get_mime_type,
)

from video_generation_mcp.helper.s3_utils import read_folder_and_upload


# from app.factory.event_data_factory import EventDataFactory
from video_generation_mcp.services.video_edit import create_thumbnail

from video_generation_mcp.helper.s3_manager import S3Uploader


from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional
from collections import defaultdict

from .video_template_processor import VideoTemplateProcessor
from .video_resolution_converter import convert_video


# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class VideoService:
    def __init__(self):
        self.s3_service = S3Uploader()
        # self.event_data_factory = EventDataFactory()
        self.video_state = None
        self.max_workers = os.cpu_count() or 4  # Default to number of CPU cores

    # def generate_video_clips_details(self, video_id, video_link, video_file_path):
    #     try:

    #         if not os.path.exists(video_file_path):
    #             if video_link:
    #                 video_file_path = save_video(
    #                     video_link,
    #                     video_id="video",
    #                     directory=f"./temp/{video_id}",
    #                 )
    #             else:
    #                 video_file_path = download_youtube_video(
    #                     script_data["link"], f"./temp/{video_id}/"
    #                 )

    #         if not video_file_path:
    #             raise Exception("Unable to download video.")

    #         return True
    #     except Exception as e:
    #         print((f"[-] Error generate_video_clips_details: {str(e)}"))
    #         raise

    def save_brand_details(
        self,
        video_id,
        user_id,
        view_type,
    ):
        try:
            brand = self.brand_repository.get_brand_details(user_id)

            if brand:
                logo = brand.get("logo")
                if logo.get("url"):
                    save_path = f"./temp/{video_id}/logo.png"
                    download_image(logo["url"], save_path)

                if VideoViewType.LANDSCAPE.value == view_type:
                    intro_landscape = brand.get("intro_landscape")
                    outro_landscape = brand.get("outro_landscape")
                    if intro_landscape:
                        save_video(
                            intro_landscape.get("url"),
                            video_id="intro_video",
                            directory=f"./temp/{video_id}",
                        )
                    if outro_landscape:
                        save_video(
                            outro_landscape.get("url"),
                            video_id="outro_video",
                            directory=f"./temp/{video_id}",
                        )
                if VideoViewType.SQUARE.value == view_type:
                    intro_square = brand.get("intro_square")
                    outro_square = brand.get("outro_square")
                    if intro_square:
                        save_video(
                            intro_square.get("url"),
                            video_id="intro_video",
                            directory=f"./temp/{video_id}",
                        )
                    if outro_square:
                        save_video(
                            outro_square.get("url"),
                            video_id="outro_video",
                            directory=f"./temp/{video_id}",
                        )

                if VideoViewType.PORTRAIT.value == view_type:
                    intro_portrait = brand.get("intro_portrait")
                    outro_portrait = brand.get("outro_portrait")
                    if intro_portrait:
                        save_video(
                            intro_portrait.get("url"),
                            video_id="intro_video",
                            directory=f"./temp/{video_id}",
                        )
                    if outro_portrait:
                        save_video(
                            outro_portrait.get("url"),
                            video_id="outro_video",
                            directory=f"./temp/{video_id}",
                        )

                return logo

            return {"position": "top-right", "size": "medium"}

        except Exception as e:
            logger.error(f"Error generating images: {str(e)}")
            raise

    def download_avatar_video(self, video_id, avatar_video_urls):
        for index, video_url in enumerate(avatar_video_urls):
            save_video(
                video_url,
                video_id=f"avatar_{index}",
                directory=f"./temp/{video_id}",
            )
        return avatar_video_urls

    def download_videos(self, video_id, video_urls, max_workers=4):
        if not video_urls:
            print("[-] No videos found to download.")
            return []

        print(f"[+] Downloading {len(video_urls)} videos...")
        video_paths = []

        def download_video_task(index, video_url):
            try:
                saved_video_path = save_video(
                    video_url, video_id=index, directory=f"./temp/{video_id}"
                )
                video_paths.append(saved_video_path)
            except Exception as e:
                print(f"[-] Could not download video: {video_url}. Error: {e}")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {
                executor.submit(download_video_task, index, video_url): video_url
                for index, video_url in enumerate(video_urls)
            }

            for future in as_completed(futures):
                video_url = futures[future]
                try:
                    future.result()
                except Exception as exc:
                    print(f"[-] Error downloading video {video_url}: {exc}")

        return video_paths

    async def download_all_assets_parallel(
        self,
        video_id: str,
        stock_videos: Optional[List[Dict[str, Any]]] = None,
        stock_images: Optional[List[Dict[str, Any]]] = None,
        event_clips: Optional[List[Dict[str, Any]]] = None,
        avatar_video_urls: Optional[List[str]] = None,
        audio_urls: Optional[List[str]] = None,
        caption: Optional[str] = None,
        intro_video: Optional[Dict[str, str]] = None,
        outro_video: Optional[Dict[str, str]] = None,
        logo: Optional[Dict[str, str]] = None,
        music: Optional[str] = None,
        max_workers: int = 10,
    ) -> Dict[str, Any]:
        """
        Download all video assets in parallel using ThreadPoolExecutor.

        Args:
            video_id: Unique identifier for the video
            stock_videos: List of stock video information
            stock_images: List of stock image information
            event_clips: List of event clip information
            avatar_video_urls: List of avatar video URLs
            audio_urls: List of audio URLs
            caption: Caption text
            intro_video: Intro video information
            outro_video: Outro video information
            logo: Logo information
            music: Music file URL
            max_workers: Maximum number of parallel downloads

        Returns:
            Dictionary containing paths to all downloaded assets
        """
        # Create temporary directory for downloads
        temp_dir = f"./temp/{video_id}"

        create_dir(temp_dir)

        downloaded_assets = {
            "video_paths": [],
            "image_paths": [],
            "audio_paths": [],
            "intro_path": None,
            "outro_path": None,
            "logo_path": None,
            "music_path": None,
        }

        # Create tasks list for parallel processing
        download_tasks = []

        def add_video_task(url: str, file_name: str, asset_type: str = "regular"):
            return {
                "type": "video",
                "url": url,
                "path": f"{temp_dir}/{file_name}",
                "asset_type": asset_type,
            }

        def add_image_task(url: str, file_name: str, asset_type: str = "regular"):
            return {
                "type": "image",
                "url": url,
                "path": f"{temp_dir}/{file_name}",
                "asset_type": asset_type,
            }

        def add_audio_task(url: str, file_name: str, asset_type: str = "regular"):
            return {
                "type": "audio",
                "url": url,
                "path": f"{temp_dir}/{file_name}",
                "asset_type": asset_type,
            }

        # Add stock videos to tasks
        if stock_videos:
            for i, video in enumerate(stock_videos):
                if "url" in video:
                    download_tasks.append(
                        add_video_task(video["url"], f"stock_video_{i}.mp4")
                    )

        # Add stock images to tasks
        if stock_images:
            for i, image in enumerate(stock_images):
                if "url" in image:
                    download_tasks.append(
                        add_image_task(image["url"], f"stock_image_{i}.jpg")
                    )

        # Add avatar videos to tasks
        if avatar_video_urls:
            for i, url in enumerate(avatar_video_urls):
                download_tasks.append(add_video_task(url, f"avatar_video_{i}.mp4"))

        # Add audio files to tasks
        if audio_urls and len(audio_urls) > 0:
            for i, url in enumerate(audio_urls):
                download_tasks.append(add_audio_task(url, f"audio_{i}.mp3"))

        # Add intro video
        if intro_video and "url" in intro_video:
            download_tasks.append(
                add_video_task(
                    intro_video["url"],
                    "intro.mp4",
                    asset_type="intro",
                )
            )

        # Add outro video
        if outro_video and "url" in outro_video:
            download_tasks.append(
                add_video_task(
                    outro_video["url"],
                    "outro.mp4",
                    asset_type="outro",
                )
            )

        # Add logo
        if logo and "url" in logo:
            download_tasks.append(
                add_image_task(logo["url"], "logo.png", asset_type="logo")
            )

        # Add music
        if music:
            download_tasks.append(
                add_audio_task(music["url"], "music.mp3", asset_type="music")
            )

        def download_asset(task):
            try:
                if task["type"] == "video":
                    save_video(
                        video_url=task["url"],
                        video_path=task.get("path"),
                    )
                    return task
                elif task["type"] == "image":
                    download_image(task["url"], task["path"])
                    return task
                elif task["type"] == "audio":
                    download_audio(task["url"], task["path"])
                    return task
            except Exception as e:
                print(f"Error downloading asset {task['url']}: {str(e)}")
                return None

        # Execute downloads in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(download_asset, task) for task in download_tasks]

            for future in as_completed(futures):
                result = future.result()
                if result:
                    if result["asset_type"] == "intro":
                        downloaded_assets["intro_path"] = result["path"]
                    elif result["asset_type"] == "outro":
                        downloaded_assets["outro_path"] = result["path"]
                    elif result["asset_type"] == "logo":
                        downloaded_assets["logo_path"] = result["path"]
                    elif result["asset_type"] == "music":
                        downloaded_assets["music_path"] = result["path"]
                    elif result["type"] == "video":
                        downloaded_assets["video_paths"].append(result["path"])
                    elif result["type"] == "image":
                        downloaded_assets["image_paths"].append(result["path"])
                    elif result["type"] == "audio":
                        downloaded_assets["audio_paths"].append(result["path"])

        return downloaded_assets

    async def process_video(self, video_data):
        """
        Process the video by combining avatar, intro, and outro clips, creating a thumbnail, uploading to S3,
        and updating the video status in the backend.

        :param video_id: The ID of the video to be processed.
        """

        video_id = str(uuid.uuid4())
        # video_id = "dd3d35b0-5335-4799-991d-930d07e69149"

        try:
            # Retrieving the data from DB
            self.video_state = MESSAGES["DATA_RETRIEVAL_STARTED"]

            view_type = video_data.get("view_type")

            stock_videos = video_data.get("stock_video_clips")
            stock_images = video_data.get("stock_image_clips")
            event_clips = video_data.get("event_stock_clips")
            audio_urls = video_data.get("audio_urls")
            avatar_video_urls = video_data.get("avatar_video_urls")
            subtitles = video_data.get("subtitles")
            caption = video_data.get("caption")
            intro_video = video_data.get("intro_video")
            outro_video = video_data.get("outro_video")
            logo = video_data.get("logo")
            music = video_data.get("music")

            # Start parallel downloads
            downloaded_assets = await self.download_all_assets_parallel(
                video_id=video_id,
                stock_videos=stock_videos,
                stock_images=stock_images,
                event_clips=event_clips,
                avatar_video_urls=avatar_video_urls,
                audio_urls=audio_urls,
                caption=caption,
                intro_video=intro_video,
                outro_video=outro_video,
                logo=logo,
                music=music,
            )

            print("downloaded_assets", downloaded_assets)

            # Process subtitle in parallel if available
            subtitle_path = None
            if subtitles:
                with ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(create_subtitle_file, video_id, subtitles)
                    subtitle_path = future.result()
                    self.video_state = MESSAGES["SUBTITLE_SAVED"]

            # All assets downloaded
            self.video_state = MESSAGES["END_DOWNLOAD"]

            # Prepare for video combination
            self.video_state = MESSAGES["START_UPLOADING"]

            # Parallel video processing tasks
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Start video combination process
                combine_future = executor.submit(
                    self.combine_videos,
                    video_id,
                    downloaded_assets["video_paths"],
                    downloaded_assets["audio_paths"],
                    view_type,
                    stock_videos,
                    stock_images,
                    event_clips,
                    subtitle_path,
                )

                # Wait for both tasks to complete
                final_video_duration = combine_future.result()

            # Create ThreadPoolExecutor for parallel processing
            with ThreadPoolExecutor(max_workers=3) as executor:
                # Submit thumbnail creation task
                thumbnail_future = executor.submit(
                    create_thumbnail,
                    f"./temp/{video_id}/final.mp4",
                    f"./temp/{video_id}/thumbnail.jpg",
                )

                # Submit video conversion task
                # conversion_future = executor.submit(convert_video, video_id, view_type)

                # Wait for thumbnail and conversion to complete
                thumbnail_future.result()
                # conversion_future.result()

                # Submit upload task after conversion is complete
                upload_future = executor.submit(read_folder_and_upload, video_id)
                uploaded_objects = upload_future.result()

            # Update video status after all parallel tasks are complete
            thumbnail, video_link = self.update_video_status(
                uploaded_objects,
                view_type,
            )

            # Cleanup temporary files
            clean_dir(f"./temp/{video_id}")

            return {
                "thumbnail": {
                    "url": thumbnail,
                    "mimetype": get_mime_type(thumbnail),
                },
                "video_link": {
                    "url": video_link,
                    "mimetype": get_mime_type(video_link),
                },
                "duration": final_video_duration,
            }

        except Exception as e:
            logger.error(f"Error processing video: {str(e)}")
            # Cleanup on error
            clean_dir(f"./temp/{video_id}")
            raise e

    def download_images(self, video_id, image_urls, max_workers=4):
        """
        Download images and save them locally using a thread pool.

        :param image_path: Path to save the images.
        :param image_urls: List of image URLs.
        :param max_workers: Maximum number of threads to use.
        """

        def download_image_task(index, url):
            """
            Task to download a single image.
            """
            save_path = f"./temp/{video_id}/image_{index + 1}.jpg"
            download_image(url, save_path)

        # Use ThreadPoolExecutor to parallelize downloads
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Create a dictionary mapping futures to their respective URLs
            future_to_url = {
                executor.submit(download_image_task, index, data["url"]): data
                for index, data in enumerate(image_urls)
            }

            # Process the results as they complete
            for future in as_completed(future_to_url):
                image_url = future_to_url[future]
                try:
                    # Attempt to get the result (this will raise any exceptions that occurred)
                    future.result()
                    print(f"[+] Successfully downloaded: {image_url}")
                except Exception as exc:
                    print(f"[-] Could not download image: {image_url}. Error: {exc}")

    def combine_videos(
        self,
        video_id,
        video_paths,
        audio_paths,
        view_type,
        stock_videos,
        stock_images,
        video_clips,
        subtitle_path,
    ):
        """Enhanced version of combine_videos with better error handling and parallel processing"""
        try:
            payload = {"video_clips": video_clips}

            # Merge stock videos and images
            merged_json = []

            if stock_videos:
                merged_json.extend(stock_videos)
            if stock_images:
                merged_json.extend(stock_images)

            payload["audio_paths"] = audio_paths

            payload["video_paths"] = video_paths

            # Create a defaultdict to store the merged data, sorted by 'at_time'
            merged_data = defaultdict(list)

            # Add data1 items to the merged_data dictionary
            for item in stock_images:
                merged_data[item["at_time"]].append({"stock_image": item})

            # Add data2 items to the merged_data dictionary
            for item in stock_videos:
                merged_data[item["at_time"]].append({"stock_video": item})

            # Sort the keys (at_time values) in increasing order
            sorted_keys = sorted(merged_data.keys())

            merged_json = [
                {"at_time": k, **{key: value for d in v for key, value in d.items()}}
                for k, v in zip(sorted_keys, [merged_data[key] for key in sorted_keys])
            ]

            payload["stock"] = merged_json

            if subtitle_path:
                payload["subtitle_path"] = subtitle_path

            if view_type == VideoViewType.LANDSCAPE.value:
                template_id = VIDEO_TEMPLATES["LANDSCAPE_TEMPLATE"]
            elif view_type == VideoViewType.PORTRAIT.value:
                template_id = VIDEO_TEMPLATES["PORTRAIT_TEMPLATE"]
            else:
                template_id = VIDEO_TEMPLATES["SQUARE_TEMPLATE"]

            # Generate video clips using template processor
            processor = VideoTemplateProcessor(template_id)

            final_duration = processor.generate_video(payload, video_id)

            return format_duration(final_duration)

        except Exception as e:
            logger.error(f"Error combining videos: {str(e)}")
            raise

    def update_video_status(
        self,
        uploaded_objects,
        video_resolution,
    ):
        """
        Update the status of the video to 'COMPLETED' in the backend.

        :param video_id: The ID of the video being processed.
        :param uploaded_objects: Uploaded files url
        :param final_video_duration: Final video duration
        """

        # Extract final video and thumbnail
        thumbnail = uploaded_objects.get("thumbnail", None)
        # Get the first item from the thumbnail array
        video_link = uploaded_objects.get("final", None)
        # Get the first item from the final array

        # Process compressed files into a simplified resolution-keyed dictionary
        compressed_files = uploaded_objects.get("compressed", [])

        compressed_videos = {}
        for file_url in compressed_files:
            # Extract the resolution from the file name (e.g., "video_1280x720.mp4")
            file_name = os.path.basename(file_url)

            resolution = file_name.split("_")[-1].split(".")[
                0
            ]  # Extract "1280x720" from "video_1280x720.mp4"

            if video_resolution == VideoViewType.PORTRAIT.value:

                short_resolution = resolution.split("x")[
                    0
                ]  # Extract the height, e.g., "720" from "720X1280"

            else:

                short_resolution = resolution.split("x")[
                    -1
                ]  # Extract the height, e.g., "720" from "1280x720"

            compressed_videos[short_resolution] = file_url

        return thumbnail, video_link

    def upload_video_with_resolution(
        self, video_id: str, resolution: str
    ) -> Dict[str, str]:
        """Helper method to convert and upload video in specific resolution"""
        try:
            input_path = f"./temp/{video_id}/final.mp4"
            output_path = f"./temp/{video_id}/final_{resolution}.mp4"

            # Convert video to desired resolution
            convert_video(input_path, output_path, resolution)

            # Upload to S3
            s3_path = f"videos/{video_id}_{resolution}.mp4"
            url = self.s3_service.upload_file(output_path, s3_path)

            return {resolution: url}
        except Exception as e:
            logger.error(f"Error processing {resolution} version: {str(e)}")
            return {}
