import json
import os
import subprocess
import tempfile

import uuid
from typing import Dict, Any, Optional
import shutil
import argparse
import logging

import re

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("VideoTemplateRenderer")


class TemplateRenderer:
    """
    Renders videos based on JSON templates.
    """

    def __init__(
        self,
        template_path: str,
        output_dir: str = "./rendered_videos",
        ffmpeg_path: str = "ffmpeg",
        temp_dir: Optional[str] = None,
    ):
        """
        Initialize the renderer with the template configuration.

        Args:
            template_path: Path to the template JSON file
            output_dir: Directory to save rendered videos
            ffmpeg_path: Path to FFmpeg executable
            temp_dir: Directory for temporary files (default: system temp)
        """
        self.ffmpeg_path = ffmpeg_path
        self.output_dir = output_dir
        self.temp_dir = temp_dir or tempfile.gettempdir()

        # Ensure directories exist
        os.makedirs(output_dir, exist_ok=True)

        # Load template data
        with open(template_path, "r") as f:
            self.template_data = json.load(f)

        # Working dirs
        self.work_dir = None
        self.session_id = None

    def _setup_working_directory(self) -> str:
        """Set up a temporary working directory for this render session."""
        self.session_id = str(uuid.uuid4())
        self.work_dir = os.path.join(self.temp_dir, f"video_render_{self.session_id}")
        os.makedirs(self.work_dir, exist_ok=True)
        return self.work_dir

    def _cleanup_working_directory(self):
        """Clean up the temporary working directory."""
        if self.work_dir and os.path.exists(self.work_dir):
            shutil.rmtree(self.work_dir)
            logger.info(f"Cleaned up working directory: {self.work_dir}")

    def _resolve_template(self, template_name: str) -> Dict[str, Any]:
        """
        Get a fully resolved template with all inherited properties.

        Args:
            template_name: Name of the template to resolve

        Returns:
            Complete template with all inherited properties
        """
        # Start with base template
        base_template = self.template_data.get("base_template", {})

        # If template_name is "base_template", return it directly
        if template_name == "base_template":
            return base_template

        # Find the specified template
        target_template = None
        for template in self.template_data.get("templates", []):
            if template.get("template_name") == template_name:
                target_template = template
                break

        if not target_template:
            raise ValueError(f"Template '{template_name}' not found")

        # Get parent template name
        parent_name = target_template.get("extends", "base_template")

        # Recursively resolve parent template
        resolved_template = self._resolve_template(parent_name)

        # Deep merge the target template into the resolved parent
        return self._deep_merge(resolved_template, target_template)

    def _deep_merge(
        self, base: Dict[str, Any], overlay: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Deep merge two dictionaries.

        Args:
            base: Base dictionary
            overlay: Dictionary to overlay on base

        Returns:
            Merged dictionary
        """
        result = base.copy()

        for key, value in overlay.items():
            # Skip inheritance key
            if key == "extends":
                continue

            # If both are dicts, merge them
            if (
                key in result
                and isinstance(result[key], dict)
                and isinstance(value, dict)
            ):
                result[key] = self._deep_merge(result[key], value)
            # If both are lists and the key is 'elements', merge by id
            elif (
                key == "elements"
                and isinstance(result.get(key), list)
                and isinstance(value, list)
            ):
                # Create a map of elements by ID in the base
                base_elements = {
                    elem.get("id"): i for i, elem in enumerate(result[key])
                }

                # Process each element in the overlay
                for overlay_elem in value:
                    elem_id = overlay_elem.get("id")
                    if elem_id in base_elements:
                        # Update existing element
                        result[key][base_elements[elem_id]] = self._deep_merge(
                            result[key][base_elements[elem_id]], overlay_elem
                        )
                    else:
                        # Add new element
                        result[key].append(overlay_elem)
            else:
                # Otherwise just replace
                result[key] = value

        return result

    def _resolve_variables(self, value: Any, context: Dict[str, Any]) -> Any:
        """
        Resolve template variables in strings, dicts, and lists.

        Args:
            value: Value to resolve variables in
            context: Context dictionary with variable values

        Returns:
            Value with variables resolved
        """
        if isinstance(value, str):
            # Replace ${var} patterns
            def replace_var(match):
                var_path = match.group(1)
                # Traverse the context dict to get the nested value
                try:
                    current = context
                    for part in var_path.split("."):
                        current = current[part]
                    return str(current)
                except (KeyError, TypeError):
                    logger.warning(f"Variable {var_path} not found in context")
                    return match.group(0)

            return re.sub(r"\${([\w.]+)}", replace_var, value)

        elif isinstance(value, list):
            return [self._resolve_variables(item, context) for item in value]

        elif isinstance(value, dict):
            return {k: self._resolve_variables(v, context) for k, v in value.items()}

        return value

    def _prepare_media_file(self, media_path: str, work_dir: str) -> str:
        """
        Copy media file to working directory and return the new path.

        Args:
            media_path: Original path to media file
            work_dir: Working directory

        Returns:
            Path to media file in working directory
        """
        if not os.path.exists(media_path):
            raise FileNotFoundError(f"Media file not found: {media_path}")

        # Create a unique filename
        filename = os.path.basename(media_path)
        unique_filename = f"{str(uuid.uuid4())[:8]}_{filename}"
        dest_path = os.path.join(work_dir, unique_filename)

        # Copy the file
        shutil.copy2(media_path, dest_path)
        return dest_path

    def _get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        Get information about a video file using FFprobe.

        Args:
            video_path: Path to video file

        Returns:
            Dictionary with video information
        """
        cmd = [
            "ffprobe",
            "-v",
            "error",
            "-select_streams",
            "v:0",
            "-show_entries",
            "stream=width,height,duration,r_frame_rate",
            "-of",
            "json",
            video_path,
        ]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            info = json.loads(result.stdout)

            # Extract frame rate as a float
            r_frame_rate = info["streams"][0].get("r_frame_rate", "30/1")
            if "/" in r_frame_rate:
                num, den = map(int, r_frame_rate.split("/"))
                fps = num / den
            else:
                fps = float(r_frame_rate)

            return {
                "width": int(info["streams"][0].get("width", 0)),
                "height": int(info["streams"][0].get("height", 0)),
                "duration": float(info["streams"][0].get("duration", 0)),
                "fps": fps,
            }
        except (subprocess.CalledProcessError, json.JSONDecodeError) as e:
            logger.error(f"Error getting video info: {e}")
            return {"width": 0, "height": 0, "duration": 0, "fps": 30}

    def _generate_filter_complex(
        self, resolved_template: Dict[str, Any], assets: Dict[str, str], work_dir: str
    ) -> str:
        """
        Generate FFmpeg filter_complex string from template.

        Args:
            resolved_template: Resolved template definition
            assets: Dict mapping element IDs to file paths
            work_dir: Working directory

        Returns:
            FFmpeg filter_complex string
        """
        elements = resolved_template.get("elements", [])
        resolution = resolved_template.get(
            "resolution", {"width": 1080, "height": 1920}
        )
        width, height = resolution["width"], resolution["height"]

        # Start building the filter_complex
        filters = []
        input_indices = {}
        next_index = 0

        # First pass: prepare inputs and assign indices
        for element in elements:
            element_id = element.get("id")
            element_type = element.get("type")

            # Skip elements that don't have assets and aren't shapes/overlays
            if element_type not in ["shape", "text"] and element_id not in assets:
                continue

            # Handle conditions
            conditions = element.get("conditions", [])
            if conditions:
                skip = False
                for condition in conditions:
                    prop = condition.get("property")
                    exists = condition.get("exists")

                    if prop == "self":
                        if exists and element_id not in assets:
                            skip = True
                            break
                        elif not exists and element_id in assets:
                            skip = True
                            break
                    elif prop:
                        if exists and prop not in assets:
                            skip = True
                            break
                        elif not exists and prop in assets:
                            skip = True
                            break

                if skip:
                    continue

            # Assign input index if this is a media element
            if element_id in assets:
                input_indices[element_id] = next_index
                next_index += 1

        # Create background color
        filters.append(f"color=c=black:s={width}x{height}:d=60[background]")

        # Process each element
        last_output = "background"
        element_outputs = {}

        for element in elements:
            element_id = element.get("id")
            element_type = element.get("type")

            # Skip elements that don't have assets and aren't shapes/overlays
            if element_type not in ["shape", "text"] and element_id not in assets:
                continue

            # Handle conditions (again, to maintain order)
            conditions = element.get("conditions", [])
            if conditions:
                skip = False
                for condition in conditions:
                    prop = condition.get("property")
                    exists = condition.get("exists")

                    if prop == "self":
                        if exists and element_id not in assets:
                            skip = True
                            break
                        elif not exists and element_id in assets:
                            skip = True
                            break
                    elif prop:
                        if exists and prop not in assets:
                            skip = True
                            break
                        elif not exists and prop in assets:
                            skip = True
                            break

                if skip:
                    continue

            # Get position data
            position = element.get("position", {})

            # Check if this is a fill position
            if position == "fill":
                x, y = 0, 0
                w, h = width, height
            else:
                # Calculate absolute position
                if "centerX" in position and position["centerX"]:
                    x = (width - position.get("width", 0)) // 2
                else:
                    x = (
                        position.get("left", 0)
                        if "left" in position
                        else width - position.get("right", 0) - position.get("width", 0)
                    )

                if "centerY" in position and position["centerY"]:
                    y = (height - position.get("height", 0)) // 2
                else:
                    y = (
                        position.get("top", 0)
                        if "top" in position
                        else height
                        - position.get("bottom", 0)
                        - position.get("height", 0)
                    )

                w = position.get("width", width)
                h = position.get("height", height)

            # Process based on element type
            if element_type == "video" and element_id in assets:
                input_idx = input_indices[element_id]

                # Scale the video to fit the position
                fit = element.get("fit", "cover")
                if fit == "cover":
                    filters.append(
                        f"[{input_idx}:v]scale={w}:{h}:force_original_aspect_ratio=increase,crop={w}:{h}[{element_id}_scaled]"
                    )
                else:  # contain
                    filters.append(
                        f"[{input_idx}:v]scale={w}:{h}:force_original_aspect_ratio=decrease,pad={w}:{h}:(ow-iw)/2:(oh-ih)/2[{element_id}_scaled]"
                    )

                # Apply mask if needed
                mask = element.get("mask", {})
                if mask and mask.get("type") == "circle" and mask.get("enabled", True):
                    # Create circle mask
                    mask_size = min(w, h)
                    filters.append(f"color=black:s={w}x{h}[{element_id}_mask_bg]")
                    filters.append(
                        f"color=white:s={mask_size}x{mask_size},geq=r='if(lte(sqrt(pow(X-(W/2),2)+pow(Y-(H/2),2)),min(W,H)/2),255,0)':g='if(lte(sqrt(pow(X-(W/2),2)+pow(Y-(H/2),2)),min(W,H)/2),255,0)':b='if(lte(sqrt(pow(X-(W/2),2)+pow(Y-(H/2),2)),min(W,H)/2),255,0)'[{element_id}_circle_mask]"
                    )
                    filters.append(
                        f"[{element_id}_circle_mask]scale={w}:{h}[{element_id}_mask]"
                    )
                    filters.append(
                        f"[{element_id}_scaled][{element_id}_mask]alphamerge[{element_id}_masked]"
                    )
                    element_source = f"{element_id}_masked"
                elif (
                    mask
                    and mask.get("type") == "rounded-rectangle"
                    and mask.get("enabled", True)
                ):
                    # Create rounded rectangle mask using overlay
                    border_radius = mask.get("borderRadius", 20)
                    filters.append(f"color=black:s={w}x{h}[{element_id}_mask_bg]")

                    # Create rounded corners using multiple overlays
                    # This is a simplified approach - for production, use a more sophisticated mask
                    filters.append(
                        f"[{element_id}_scaled]format=rgba,geq=r='r(X,Y)':g='g(X,Y)':b='b(X,Y)':a='if(and(and(gt(X,{border_radius}),lt(X,{w-border_radius})),and(gt(Y,{border_radius}),lt(Y,{h-border_radius}))),255,if(and(and(gt(X,0),lt(X,{border_radius})),and(gt(Y,0),lt(Y,{border_radius}))),if(lte(sqrt(pow(X-{border_radius},2)+pow(Y-{border_radius},2)),{border_radius}),255,0),if(and(and(gt(X,{w-border_radius}),lt(X,{w})),and(gt(Y,0),lt(Y,{border_radius}))),if(lte(sqrt(pow(X-{w-border_radius},2)+pow(Y-{border_radius},2)),{border_radius}),255,0),if(and(and(gt(X,0),lt(X,{border_radius})),and(gt(Y,{h-border_radius}),lt(Y,{h}))),if(lte(sqrt(pow(X-{border_radius},2)+pow(Y-{h-border_radius},2)),{border_radius}),255,0),if(and(and(gt(X,{w-border_radius}),lt(X,{w})),and(gt(Y,{h-border_radius}),lt(Y,{h}))),if(lte(sqrt(pow(X-{w-border_radius},2)+pow(Y-{h-border_radius},2)),{border_radius}),255,0),0))))))'[{element_id}_masked]"
                    )
                    element_source = f"{element_id}_masked"
                else:
                    element_source = f"{element_id}_scaled"

                # Overlay on previous result
                filters.append(
                    f"[{last_output}][{element_source}]overlay={x}:{y}[{element_id}_out]"
                )
                last_output = f"{element_id}_out"
                element_outputs[element_id] = last_output

            elif element_type == "image" and element_id in assets:
                input_idx = input_indices[element_id]

                # Scale the image
                fit = element.get("fit", "cover")
                if fit == "cover":
                    filters.append(
                        f"[{input_idx}:v]scale={w}:{h}:force_original_aspect_ratio=increase,crop={w}:{h}[{element_id}_scaled]"
                    )
                else:  # contain
                    filters.append(
                        f"[{input_idx}:v]scale={w}:{h}:force_original_aspect_ratio=decrease,pad={w}:{h}:(ow-iw)/2:(oh-ih)/2[{element_id}_scaled]"
                    )

                # Apply circular mask if specified
                style = element.get("style", {})
                if style.get("circle"):
                    # Create circle mask
                    mask_size = min(w, h)
                    filters.append(f"color=black:s={w}x{h}[{element_id}_mask_bg]")
                    filters.append(
                        f"color=white:s={mask_size}x{mask_size},geq=r='if(lte(sqrt(pow(X-(W/2),2)+pow(Y-(H/2),2)),min(W,H)/2),255,0)':g='if(lte(sqrt(pow(X-(W/2),2)+pow(Y-(H/2),2)),min(W,H)/2),255,0)':b='if(lte(sqrt(pow(X-(W/2),2)+pow(Y-(H/2),2)),min(W,H)/2),255,0)'[{element_id}_circle_mask]"
                    )
                    filters.append(
                        f"[{element_id}_circle_mask]scale={w}:{h}[{element_id}_mask]"
                    )
                    filters.append(
                        f"[{element_id}_scaled][{element_id}_mask]alphamerge[{element_id}_masked]"
                    )
                    element_source = f"{element_id}_masked"
                else:
                    element_source = f"{element_id}_scaled"

                # Overlay on previous result
                filters.append(
                    f"[{last_output}][{element_source}]overlay={x}:{y}[{element_id}_out]"
                )
                last_output = f"{element_id}_out"
                element_outputs[element_id] = last_output

            elif element_type == "shape":
                shape = element.get("shape", "rectangle")
                style = element.get("style", {})

                if shape == "rectangle":
                    color = style.get("color", "white")

                    # Handle gradient if specified
                    gradient = style.get("gradient")
                    if gradient:
                        gradient_type = gradient.get("type", "linear")
                        colors = gradient.get("colors", ["white", "black"])
                        angle = gradient.get("angle", 0)

                        # Create gradient (simplified version - would need more complex filter for real gradients)
                        filters.append(
                            f"color=white:s={w}x{h},format=rgba,gradients=n=2:c0={colors[0]}:c1={colors[1]}:x0=0:y0=0:x1={w}:y1={h}[{element_id}_shape]"
                        )
                    else:
                        filters.append(f"color={color}:s={w}x{h}[{element_id}_shape]")

                elif shape == "circle":
                    color = style.get("color", "white")
                    radius = min(w, h) // 2

                    # Create circle
                    filters.append(f"color=black:s={w}x{h}[{element_id}_bg]")
                    filters.append(
                        f"color={color}:s={radius*2}x{radius*2},format=rgba,geq=r='r(X,Y)':g='g(X,Y)':b='b(X,Y)':a='if(lte(sqrt(pow(X-(W/2),2)+pow(Y-(H/2),2)),{radius}),255,0)'[{element_id}_circle]"
                    )
                    filters.append(
                        f"[{element_id}_bg][{element_id}_circle]overlay=({w}-{radius*2})/2:({h}-{radius*2})/2[{element_id}_shape]"
                    )

                # Apply opacity
                opacity = style.get("opacity", 1.0)
                if opacity < 1.0:
                    filters.append(
                        f"[{element_id}_shape]format=rgba,colorchannelmixer=aa={opacity}[{element_id}_shape_opacity]"
                    )
                    shape_source = f"{element_id}_shape_opacity"
                else:
                    shape_source = f"{element_id}_shape"

                # Overlay on previous result
                filters.append(
                    f"[{last_output}][{shape_source}]overlay={x}:{y}[{element_id}_out]"
                )
                last_output = f"{element_id}_out"
                element_outputs[element_id] = last_output

            elif element_type == "text" and element.get("enabled", True):
                # Generate a text file with the content
                content = element.get("content", "Sample Text")
                text_file = os.path.join(work_dir, f"{element_id}_text.txt")
                with open(text_file, "w") as f:
                    f.write(content)

                # Get text style
                text_style = element.get("style", {})
                font_family = text_style.get("fontFamily", "Sans")
                font_size = text_style.get("fontSize", 72)
                font_color = text_style.get("color", "white")
                font_weight = (
                    "bold" if text_style.get("fontWeight") == "bold" else "normal"
                )
                text_align = text_style.get("textAlign", "left")

                # Background style
                bg_style = text_style.get("background", {})
                bg_color = bg_style.get("color", "none")
                bg_padding = bg_style.get("padding", 0)
                border_radius = bg_style.get("borderRadius", 0)

                # Create text overlay
                # For simplicity, we're using drawtext, but in production you might want a more sophisticated approach
                text_filter = f"drawtext=fontfile='{os.path.join(os.getcwd(), 'assets/fonts/Arial.ttf')}'"
                text_filter += f":text='{content}'"
                text_filter += f":fontsize={font_size}"
                text_filter += f":fontcolor={font_color}"
                if font_weight == "bold":
                    text_filter += ":fontweight=bold"

                # Position based on text alignment
                if text_align == "center":
                    text_filter += f":x=(w-text_w)/2+{x}"
                elif text_align == "right":
                    text_filter += f":x={x+w}-text_w"
                else:  # left
                    text_filter += f":x={x}"

                text_filter += f":y={y}"

                # Background box (simplified)
                if bg_color != "none":
                    text_filter += f":box=1:boxcolor={bg_color}:boxborderw={bg_padding}"

                # Apply text filter
                filters.append(f"[{last_output}]{text_filter}[{element_id}_out]")
                last_output = f"{element_id}_out"
                element_outputs[element_id] = last_output

        # Combine all filters
        return ";".join(filters)

    def render(
        self,
        template_name: str,
        assets: Dict[str, str],
        output_filename: str,
        duration: int = 30,
        custom_properties: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Render a video from a template and assets.

        Args:
            template_name: Name of the template to use
            assets: Dictionary mapping element IDs to file paths
            output_filename: Name of the output file
            duration: Video duration in seconds
            custom_properties: Custom properties to override in the template

        Returns:
            Path to the rendered video file
        """
        try:
            # Set up working directory
            work_dir = self._setup_working_directory()
            logger.info(f"Working directory: {work_dir}")

            # Resolve template
            resolved_template = self._resolve_template(template_name)
            logger.info(f"Resolved template: {template_name}")

            # Apply custom properties if provided
            if custom_properties:
                resolved_template = self._deep_merge(
                    resolved_template, custom_properties
                )

            # Resolve template variables
            context = {
                "customization": resolved_template.get("customization", {}),
                "assets": assets,
            }
            resolved_template = self._resolve_variables(resolved_template, context)

            # Prepare input files
            prepared_assets = {}
            input_args = []

            for element_id, file_path in assets.items():
                if file_path:
                    # Copy to working directory
                    new_path = self._prepare_media_file(file_path, work_dir)
                    prepared_assets[element_id] = new_path

                    # Add to input arguments
                    input_args.extend(["-i", new_path])

            # Generate filter complex
            filter_complex = self._generate_filter_complex(
                resolved_template, prepared_assets, work_dir
            )

            # Prepare output path
            if not output_filename.endswith((".mp4", ".mov")):
                output_filename += ".mp4"
            output_path = os.path.join(self.output_dir, output_filename)

            # Build FFmpeg command
            cmd = [
                self.ffmpeg_path,
                "-y",  # Overwrite output file
            ]

            # Add input files
            cmd.extend(input_args)

            # Add filter complex
            cmd.extend(
                [
                    "-filter_complex",
                    filter_complex,
                    "-map",
                    (
                        "[final]"
                        if "final" in filter_complex
                        else f"[{resolved_template['elements'][-1]['id']}_out]"
                    ),
                    "-c:v",
                    "libx264",
                    "-preset",
                    "medium",
                    "-crf",
                    "23",
                    "-t",
                    str(duration),
                    output_path,
                ]
            )

            # Execute FFmpeg command
            logger.info(f"Executing FFmpeg command: {' '.join(cmd)}")
            subprocess.run(cmd, check=True)

            logger.info(f"Video rendered successfully: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Error rendering video: {e}")
            raise
        finally:
            # Clean up
            self._cleanup_working_directory()


def main():
    """Main function to run from command line."""
    parser = argparse.ArgumentParser(description="Video Template Renderer")

    parser.add_argument("--template", required=True, help="Path to template JSON file")
    parser.add_argument(
        "--template-name", default="base_template", help="Template name to use"
    )
    parser.add_argument("--output", required=True, help="Output filename")
    parser.add_argument(
        "--duration", type=int, default=30, help="Video duration in seconds"
    )
    parser.add_argument("--ffmpeg", default="ffmpeg", help="Path to FFmpeg executable")
    parser.add_argument(
        "--assets", nargs="+", help="Assets in format elementId:filePath"
    )

    args = parser.parse_args()

    # Parse assets
    assets = {}
    if args.assets:
        for asset in args.assets:
            try:
                element_id, file_path = asset.split(":", 1)
                assets[element_id] = file_path
            except ValueError:
                parser.error(f"Invalid asset format: {asset}. Use elementId:filePath")

    # Create renderer
    renderer = TemplateRenderer(template_path=args.template, ffmpeg_path=args.ffmpeg)

    # Render video
    try:
        output_path = renderer.render(
            template_name=args.template_name,
            assets=assets,
            output_filename=args.output,
            duration=args.duration,
        )
        print(f"Video rendered successfully: {output_path}")
    except Exception as e:
        logger.error(f"Failed to render video: {e}")
        sys.exit(1)


if __name__ == "__main__":
    import sys

    main()
