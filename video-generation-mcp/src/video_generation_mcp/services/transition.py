import numpy as np


def add_slow_fades(
    video,
    fade_in_duration=0.2,
    fade_out_duration=0.2,
):
    """
    Add slow, gradual fade-in and fade-out effects to a video clip.

    Parameters:
    -----------
    video_path : str
        Path to the input video file
    output_path : str
        Path where the output video will be saved
    fade_in_duration : float, optional
        Duration of the fade-in effect in seconds (default: 3.0)
    fade_out_duration : float, optional
        Duration of the fade-out effect in seconds (default: 3.0)

    Returns:
    --------
    None
        The function saves the processed video to the output_path
    """
    try:

        # Get the total duration of the video
        total_duration = video.duration

        # Create a custom fade-in effect with a more gradual curve
        # This creates a slow, gentle fade instead of the default linear fade
        def slow_fadein(t):
            # Using a quadratic function for a smoother, slower start
            if t < fade_in_duration:
                return np.sqrt(t / fade_in_duration)
            else:
                return 1.0

        # Create a custom fade-out effect with a more gradual curve
        def slow_fadeout(t):
            # Using a quadratic function for a smoother, slower end
            if t > total_duration - fade_out_duration:
                remaining = total_duration - t
                return np.sqrt(remaining / fade_out_duration)
            else:
                return 1.0

        # Apply both fade effects
        video_with_fades = video.fl(
            lambda gf, t: gf(t) * slow_fadein(t) * slow_fadeout(t)
        )

        return video_with_fades

    except Exception as e:
        print(f"Error processing video: {str(e)}")
