import os
from moviepy.editor import TextClip, CompositeVideoClip
import pysrt
from functools import lru_cache


# Use lru_cache as a stand-in for your cached_operation decorator
@lru_cache(maxsize=32)
def create_text_generator(
    font_path="./asset/font/bold_font.ttf", font_size=50, color="#FFFF00"
):
    # Convert to absolute path to make sure it's properly resolved
    default_font = os.path.abspath("./asset/font/bold_font.ttf")

    # Ensure font_path is not None
    if font_path is None:
        print("Warning: font_path was None, using default font")
        font_path = default_font

    # Print the actual paths for debugging
    print(f"Requested font path: {font_path}")
    print(f"Default font path: {default_font}")

    # Check if the requested font exists
    if not os.path.exists(font_path):
        print(f"Warning: Font file does not exist at {font_path}")
        # Fallback to Arial which should be available on most systems
        print("Falling back to Arial font")
        return lambda txt: TextClip(
            txt=txt.upper(),
            font="Arial",
            fontsize=font_size,
            color=color,
            stroke_color="Black",
            stroke_width=2,
            align="center",
            method="label",
        )

    # Return the text clip generator with a guaranteed valid font_path
    return lambda txt: TextClip(
        txt=txt.upper(),
        font="Arial",  # Using Arial as you did in your original code
        fontsize=font_size,
        color=color,
        stroke_color="Black",
        stroke_width=2,
        align="center",
        method="label",
    )


def create_subtitles_clip(subtitle_path, generator, position, video_duration=50):
    """
    Create a custom subtitles clip from an SRT file

    Args:
        subtitle_path: Path to the .srt file
        generator: TextClip generator function
        position: Position tuple/dict (x, y) or {'x': x, 'y': y}
        video_duration: Duration of the video in seconds

    Returns:
        CompositeVideoClip with all subtitle text clips
    """
    print(f"Processing subtitle file: {subtitle_path}")

    # Verify the subtitle file exists
    if not os.path.exists(subtitle_path):
        print(f"Error: Subtitle file not found at {subtitle_path}")
        return TextClip(" ", font="Arial").set_duration(video_duration)

    try:
        # Parse subtitles with pysrt
        subs = pysrt.open(subtitle_path, encoding="utf-8")
        print(f"Found {len(subs)} subtitles in file")

        # Create a list to hold all subtitle clips
        subtitle_clips = []

        # Process each subtitle entry
        for i, sub in enumerate(subs):
            print(f"Processing subtitle {i+1}: {sub.start} --> {sub.end}")

            # Convert times to seconds
            start_time = sub.start.ordinal / 1000.0
            end_time = sub.end.ordinal / 1000.0
            duration = end_time - start_time

            # Create text clip for this subtitle
            try:
                txt_clip = generator(sub.text)

                # Set timing and position
                txt_clip = txt_clip.set_start(start_time).set_duration(duration)

                # Handle different position formats
                if isinstance(position, dict):
                    pos = (position.get("x", "center"), position.get("y", "bottom"))
                else:
                    pos = position

                txt_clip = txt_clip.set_position(pos)

                # Add to our collection
                subtitle_clips.append(txt_clip)
                print(f"Added subtitle {i+1} from {start_time:.2f}s to {end_time:.2f}s")

            except Exception as e:
                print(f"Error creating TextClip for subtitle {i+1}: {str(e)}")

        # If we have subtitle clips, combine them
        if subtitle_clips:
            # Create a CompositeVideoClip containing all subtitle clips
            final_clip = CompositeVideoClip(subtitle_clips, size=(1920, 1080))

            # Ensure the clip has a duration (use maximum end time of subtitles or video_duration)
            max_subtitle_end = max([sub.end.ordinal / 1000.0 for sub in subs])
            final_duration = max(max_subtitle_end, video_duration)
            final_clip = final_clip.set_duration(final_duration)

            print(
                f"Created subtitle composite clip with duration {final_duration:.2f}s"
            )
            return final_clip
        else:
            print("No valid subtitle clips were created")
            return TextClip(" ", font="Arial").set_duration(video_duration)

    except Exception as e:
        print(f"Error processing subtitles: {str(e)}")
        return TextClip(" ", font="Arial").set_duration(video_duration)


def process_subtitle_element(element, clip_data_object, video_duration=50):
    """
    Process a subtitle element and return a clip

    Args:
        element: The subtitle element definition
        clip_data_object: Object containing subtitle path and other data
        video_duration: Duration of the video in seconds

    Returns:
        A video clip with the subtitles
    """
    position = element["position"]
    style = element["style"]

    caption = clip_data_object.get("caption", {})
    subtitle_path = clip_data_object.get("subtitle_path")

    print(f"Subtitle path: {subtitle_path}")
    subtitle_path = os.path.abspath(subtitle_path)

    # Create the text generator
    font_path = (
        caption.get("font_path", "./asset/font/bold_font.ttf")
        if caption
        else style.get("fontPath", "./asset/font/bold_font.ttf")
    )
    font_size = style.get("fontSize", 50)
    color = caption.get("color_code", "#FFFF00") if caption else "#FFFF00"

    # Create text generator
    generator = create_text_generator(
        font_path=font_path,
        font_size=font_size,
        color=color,
    )

    # Create and return the subtitles clip
    return create_subtitles_clip(subtitle_path, generator, position, video_duration)
