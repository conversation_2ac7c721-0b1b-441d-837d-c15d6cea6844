REEL_TEMPLATE = [
    {
        "template_name": "split_screen",
        "extends": "base_template",
        "customization": {
            "colors": {
                "primary": "#FF6B6B",
                "secondary": "#4ECDC4",
                "background": "#151515",
                "text": "#FFFFFF",
            }
        },
        "elements": [
            {
                "id": "stock_clip",
                "position": {"top": 0, "centerX": True, "width": 1080, "height": 940},
            },
            {
                "id": "avatar_video",
                "position": {
                    "bottom": 40,
                    "centerX": True,
                    "width": 1080,
                    "height": 940,
                },
            },
            {
                "id": "divider",
                "type": "shape",
                "shape": "rectangle",
                "position": {"centerY": True, "width": 1080, "height": 8},
                "style": {
                    "gradient": {
                        "type": "linear",
                        "colors": [
                            "${customization.colors.primary}",
                            "${customization.colors.secondary}",
                        ],
                        "angle": 90,
                    }
                },
                "zIndex": 3,
            },
        ],
    },
    {
        "template_name": "picture_in_picture",
        "extends": "base_template",
        "customization": {
            "colors": {
                "primary": "#6B5BFF",
                "secondary": "#FF5BAA",
                "background": "#0A0A0A",
                "text": "#FFFFFF",
            }
        },
        "elements": [
            {"id": "stock_clip", "position": "fill"},
            {
                "id": "avatar_video",
                "position": {"bottom": 120, "right": 80, "width": 400, "height": 400},
                "mask": {"type": "rounded-rectangle", "borderRadius": 20},
                "style": {
                    "border": {
                        "width": 6,
                        "gradient": [
                            "${customization.colors.primary}",
                            "${customization.colors.secondary}",
                        ],
                    },
                    "shadow": {
                        "color": "rgba(0,0,0,0.5)",
                        "blur": 15,
                        "offsetX": 0,
                        "offsetY": 5,
                    },
                },
            },
        ],
    },
    {
        "template_name": "side_by_side",
        "extends": "base_template",
        "customization": {
            "colors": {
                "primary": "#00BFA6",
                "secondary": "#F25F5C",
                "background": "#0D1B2A",
                "text": "#FFFFFF",
            }
        },
        "elements": [
            {
                "id": "stock_clip",
                "position": {"top": 0, "left": 0, "width": 540, "height": 1920},
                "conditions": [{"property": "self", "exists": True}],
            },
            {
                "id": "avatar_video",
                "position": {"top": 0, "right": 0, "width": 540, "height": 1920},
                "conditions": [{"property": "stock_clip", "exists": True}],
            },
            {
                "id": "avatar_video_center",
                "position": {
                    "centerX": True,
                    "centerY": True,
                    "width": 800,
                    "height": 800,
                },
                "conditions": [{"property": "stock_clip", "exists": False}],
            },
            {
                "id": "divider",
                "type": "shape",
                "shape": "rectangle",
                "position": {"centerX": True, "width": 8, "height": 1920},
                "style": {
                    "gradient": {
                        "type": "linear",
                        "colors": [
                            "${customization.colors.primary}",
                            "${customization.colors.secondary}",
                        ],
                        "angle": 180,
                    }
                },
                "zIndex": 3,
                "conditions": [{"property": "stock_clip", "exists": True}],
            },
        ],
    },
    {
        "template_name": "floating_panels",
        "extends": "base_template",
        "customization": {
            "colors": {
                "primary": "#667EEA",
                "secondary": "#764BA2",
                "background": "#0F172A",
                "text": "#FFFFFF",
            },
            "animations": {
                "default_in": {
                    "type": "slideIn",
                    "from": "bottom",
                    "duration": 0.6,
                    "easing": "easeOutBack",
                },
                "default_out": {
                    "type": "slideOut",
                    "to": "bottom",
                    "duration": 0.4,
                    "easing": "easeInBack",
                },
            },
        },
        "elements": [
            {
                "id": "stock_clip",
                "position": {"top": 200, "centerX": True, "width": 900, "height": 700},
                "mask": {"type": "rounded-rectangle", "borderRadius": 20},
                "style": {
                    "border": {"width": 4, "color": "${customization.colors.primary}"},
                    "shadow": {
                        "color": "rgba(0,0,0,0.5)",
                        "blur": 15,
                        "offsetX": 0,
                        "offsetY": 5,
                    },
                },
            },
            {
                "id": "avatar_video",
                "position": {
                    "bottom": 200,
                    "centerX": True,
                    "width": 900,
                    "height": 700,
                },
                "mask": {"type": "rounded-rectangle", "borderRadius": 20},
                "style": {
                    "border": {
                        "width": 4,
                        "color": "${customization.colors.secondary}",
                    },
                    "shadow": {
                        "color": "rgba(0,0,0,0.5)",
                        "blur": 15,
                        "offsetX": 0,
                        "offsetY": 5,
                    },
                },
            },
            {
                "id": "decoration",
                "type": "shape",
                "shape": "circle",
                "position": {"bottom": 950, "right": 120, "width": 100, "height": 100},
                "style": {
                    "gradient": {
                        "type": "radial",
                        "colors": [
                            "${customization.colors.primary}",
                            "${customization.colors.secondary}",
                        ],
                    },
                    "opacity": 0.8,
                },
                "animation": {
                    "type": "float",
                    "duration": 3,
                    "repeat": -1,
                    "amplitude": 10,
                },
                "zIndex": 1,
            },
        ],
    },
]
