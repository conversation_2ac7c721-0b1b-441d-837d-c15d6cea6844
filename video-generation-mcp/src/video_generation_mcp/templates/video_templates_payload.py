# Shorts template payload
VIDEO_TEMPLATES = {
    "portrait_template": {
        "template_name": "shorts_template",
        "aspect_ratio": {"width": 9, "height": 16},
        "resolution": {"width": 1080, "height": 1920},
        "elements": [
            {
                "id": "background",
                "type": "video",
                "position": "fill",
                "fit": "cover",
                "zIndex": 0,
            },
            {
                "id": "logo",
                "type": "image",
                "position": {
                    "top": 60,
                    "right": 60,
                    "width": 150,
                    "height": 150,
                },
                "style": {
                    "circle": True,
                    "background": "white",
                    "border": {
                        "width": 4,
                        "gradient": ["#8A2BE2", "#DA70D6"],  # Purple gradient
                    },
                },
                "fit": "contain",
                "zIndex": 2,
            },
            {
                "id": "main_video",
                "type": "video",
                "position": {
                    "bottom": 760,
                    "centerX": True,
                    "width": 400,
                    "height": 400,
                },
                "mask": {"type": "circle"},
                "fit": "cover",
                "zIndex": 1,
            },
            {
                "id": "main_audio",
                "type": "audio",
            },
            {
                "id": "subtitles",
                "type": "text",
                "enabled": True,
                "position": {
                    "x": "center",
                    "y": 1500,
                    "bottom": 200,
                    "centerX": True,
                    "width": 960,
                    "maxWidth": 960,
                },
                "style": {
                    "fontFamily": "Montserrat",
                    "fontSize": 100,
                    "fontWeight": "bold",
                    "color": "white",
                    "textAlign": "center",
                    "background": {
                        "color": "rgba(0, 0, 0, 0.6)",
                        "padding": 14,
                        "borderRadius": 12,
                    },
                    "stroke": {"color": "black", "width": 2},
                    "lineHeight": 1.3,
                    "wordWrap": True,
                    "maxLines": 3,
                    "overflow": "ellipsis",
                    "fontPath": "./asset/font/bold_font.ttf",
                },
                "animation": {
                    "in": {
                        "type": "fadeIn",
                        "duration": 0.5,
                        "delay": 0.2,
                        "easing": "easeOutQuad",
                    },
                    "out": {"type": "fadeOut", "duration": 0.5, "easing": "easeInQuad"},
                },
                "zIndex": 3,
            },
            {
                "id": "clip",
                "type": "video",
                "size": {
                    "width": 1080,
                    "height": 1920,
                    "aspectRatio": "9:16",
                    "maintainAspectRatio": True,
                    "fitMethod": "cover",
                },
                "position": {
                    "x": 0,
                    "y": 0,
                    "centerX": True,
                },
                "mask": {
                    "enabled": False,
                    "type": "image",
                    "source": None,
                    "invert": False,
                },
                "effects": {
                    "opacity": 1.0,
                    "blur": 0,
                    "brightness": 1.0,
                    "contrast": 1.0,
                    "saturation": 1.0,
                    "filters": [
                        {
                            "type": "colorCorrection",
                            "settings": {"gamma": 1.0, "temperature": 0, "tint": 0},
                        }
                    ],
                },
                "transitions": {
                    "in": {"type": "fade", "duration": 1.0, "easing": "easeInOut"},
                    "out": {"type": "fade", "duration": 1.0, "easing": "easeInOut"},
                },
            },
        ],
    },
    "landscape_template": {
        "template_name": "youtube_template",
        "aspect_ratio": {"width": 16, "height": 9},
        "resolution": {"width": 1920, "height": 1080},
        "elements": [
            {
                "id": "background",
                "type": "video",
                "position": "fill",
                "fit": "cover",
                "zIndex": 0,
            },
            {
                "id": "logo",
                "type": "image",
                "position": {
                    "top": 50,
                    "right": 50,
                    "width": 120,
                    "height": 120,
                },
                "style": {
                    "circle": True,
                    "background": "white",
                    "border": {
                        "width": 4,
                        "gradient": ["#8A2BE2", "#DA70D6"],  # Purple gradient
                    },
                },
                "fit": "contain",
                "zIndex": 2,
            },
            {
                "id": "main_video",
                "type": "video",
                "position": {
                    "bottom": 50,
                    "centerX": True,
                    "left": 50,  # Move to the left side
                    "width": 400,
                    "height": 225,
                },
                "mask": {"type": "circle"},
                "fit": "cover",
                "zIndex": 1,
            },
            {
                "id": "main_audio",
                "type": "audio",
            },
            {
                "id": "subtitles",
                "type": "text",
                "enabled": False,
                "position": {
                    "x": "center",
                    "y": 1500,
                    "bottom": 200,
                    "centerX": True,
                    "width": 960,
                    "maxWidth": 960,
                },
                "style": {
                    "fontFamily": "Montserrat",
                    "fontSize": 100,
                    "fontWeight": "bold",
                    "color": "white",
                    "textAlign": "center",
                    "background": {
                        "color": "rgba(0, 0, 0, 0.6)",
                        "padding": 14,
                        "borderRadius": 12,
                    },
                    "stroke": {"color": "black", "width": 2},
                    "lineHeight": 1.3,
                    "wordWrap": True,
                    "maxLines": 3,
                    "overflow": "ellipsis",
                    "fontPath": "./asset/font/bold_font.ttf",
                },
                "animation": {
                    "in": {
                        "type": "fadeIn",
                        "duration": 0.5,
                        "delay": 0.2,
                        "easing": "easeOutQuad",
                    },
                    "out": {"type": "fadeOut", "duration": 0.5, "easing": "easeInQuad"},
                },
                "zIndex": 3,
            },
            {
                "id": "clip",
                "type": "video",
                "size": {
                    "width": 1920,
                    "height": 1080,
                    "aspectRatio": "16:9",
                    "maintainAspectRatio": True,
                    "fitMethod": "cover",
                },
                "position": {
                    "x": 0,
                    "y": 0,
                    "centerX": True,
                },
                "mask": {
                    "enabled": False,
                    "type": "image",
                    "source": None,
                    "invert": False,
                },
                "effects": {
                    "opacity": 1.0,
                    "blur": 0,
                    "brightness": 1.0,
                    "contrast": 1.0,
                    "saturation": 1.0,
                    "filters": [
                        {
                            "type": "colorCorrection",
                            "settings": {"gamma": 1.0, "temperature": 0, "tint": 0},
                        }
                    ],
                },
                "transitions": {
                    "in": {"type": "fade", "duration": 1.0, "easing": "easeInOut"},
                    "out": {"type": "fade", "duration": 1.0, "easing": "easeInOut"},
                },
            },
        ],
    },
    "square_template": {
        "template_name": "linkedin_brand_template_square",
        "aspect_ratio": {"width": 1, "height": 1},
        "resolution": {"width": 1080, "height": 1080},
        "elements": [
            {
                "id": "background",
                "type": "video",
                "position": "fill",
                "fit": "cover",
                "zIndex": 0,
            },
            {
                "id": "logo",
                "type": "image",
                "position": {
                    "top": 40,
                    "right": 40,
                    "width": 100,
                    "height": 100,
                },
                "style": {
                    "circle": True,
                    "background": "white",
                    "border": {
                        "width": 4,
                        "gradient": ["#8A2BE2", "#DA70D6"],  # Purple gradient
                    },
                },
                "fit": "contain",
                "zIndex": 2,
            },
            {
                "id": "main_video",
                "type": "video",
                "position": {
                    "bottom": 50,
                    "centerX": True,
                    "left": 50,  # Move to the left side
                    "width": 300,
                    "height": 168,
                },
                "mask": {"type": "circle"},
                "fit": "cover",
                "zIndex": 1,
            },
            {
                "id": "main_audio",
                "type": "audio",
            },
            {
                "id": "clip",
                "type": "video",
                "size": {
                    "width": 1080,
                    "height": 1080,
                    "aspectRatio": "1:1",
                    "maintainAspectRatio": True,
                    "fitMethod": "cover",
                },
                "position": {"x": 0, "y": 0, "centerX": True},
                "mask": {
                    "enabled": False,
                    "type": "image",
                    "source": None,
                    "invert": False,
                },
                "effects": {
                    "opacity": 1.0,
                    "blur": 0,
                    "brightness": 1.0,
                    "contrast": 1.0,
                    "saturation": 1.0,
                    "filters": [
                        {
                            "type": "colorCorrection",
                            "settings": {"gamma": 1.0, "temperature": 0, "tint": 0},
                        }
                    ],
                },
                "transitions": {
                    "in": {"type": "fade", "duration": 1.0, "easing": "easeInOut"},
                    "out": {"type": "fade", "duration": 1.0, "easing": "easeInOut"},
                },
            },
        ],
    },
}
