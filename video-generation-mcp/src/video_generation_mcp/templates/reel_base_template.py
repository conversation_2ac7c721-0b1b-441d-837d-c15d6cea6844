BASE_REEL_TEMPLATE = {
    "base_template": {
        "template_name": "base_template",
        "aspect_ratio": {"width": 9, "height": 16},
        "resolution": {"width": 1080, "height": 1920},
        "customization": {
            "colors": {
                "primary": "#FF6B6B",
                "secondary": "#4ECDC4",
                "background": "#000000",
                "text": "#FFFFFF",
                "overlay": "rgba(0, 0, 0, 0.6)",
            },
            "fonts": {
                "primary": {
                    "family": "Montserrat",
                    "weights": ["regular", "bold", "semibold"],
                    "path": "./assets/fonts/",
                },
                "secondary": {
                    "family": "Roboto",
                    "weights": ["regular", "bold"],
                    "path": "./assets/fonts/",
                },
            },
            "animations": {
                "default_in": {
                    "type": "fadeIn",
                    "duration": 0.5,
                    "easing": "easeOutQuad",
                },
                "default_out": {
                    "type": "fadeOut",
                    "duration": 0.5,
                    "easing": "easeInQuad",
                },
                "text_in": {
                    "type": "slideUp",
                    "duration": 0.4,
                    "easing": "easeOutBack",
                },
                "text_out": {
                    "type": "slideDown",
                    "duration": 0.4,
                    "easing": "easeInBack",
                },
            },
            "borders": {"radius": 10, "width": 3},
        },
        "elements": [
            {
                "id": "background",
                "type": "image",
                "position": "fill",
                "fit": "cover",
                "style": {"background": "${customization.colors.background}"},
                "zIndex": 0,
                "customizable": True,
            },
            {
                "id": "stock_clip",
                "type": "video",
                "required": False,
                "position": {"top": 0, "centerX": True, "width": 1080, "height": 960},
                "fit": "cover",
                "zIndex": 1,
                "transitions": {
                    "in": {"type": "fade", "duration": 0.5, "easing": "easeInOut"},
                    "out": {"type": "fade", "duration": 0.5, "easing": "easeInOut"},
                },
                "conditions": [{"property": "self", "exists": True}],
                "customizable": True,
            },
            {
                "id": "avatar_video",
                "type": "video",
                "position": {
                    "bottom": 100,
                    "centerX": True,
                    "width": 1080,
                    "height": 960,
                },
                "fit": "cover",
                "zIndex": 2,
                "transitions": {
                    "in": {
                        "type": "${customization.animations.default_in.type}",
                        "duration": "${customization.animations.default_in.duration}",
                        "easing": "${customization.animations.default_in.easing}",
                    },
                    "out": {
                        "type": "${customization.animations.default_out.type}",
                        "duration": "${customization.animations.default_out.duration}",
                        "easing": "${customization.animations.default_out.easing}",
                    },
                },
                "conditions": [{"property": "stock_clip", "exists": True}],
                "customizable": True,
            },
            {
                "id": "avatar_video_center",
                "type": "video",
                "position": {
                    "centerX": True,
                    "centerY": True,
                    "width": 800,
                    "height": 800,
                },
                "mask": {"type": "circle", "enabled": True},
                "fit": "cover",
                "zIndex": 3,
                "transitions": {
                    "in": {
                        "type": "${customization.animations.default_in.type}",
                        "duration": "${customization.animations.default_in.duration}",
                        "easing": "${customization.animations.default_in.easing}",
                    },
                    "out": {
                        "type": "${customization.animations.default_out.type}",
                        "duration": "${customization.animations.default_out.duration}",
                        "easing": "${customization.animations.default_out.easing}",
                    },
                },
                "conditions": [{"property": "stock_clip", "exists": False}],
                "customizable": True,
            },
            {
                "id": "main_audio",
                "type": "audio",
                "volume": 1.0,
                "fadeIn": 0.5,
                "fadeOut": 0.5,
                "zIndex": 4,
                "customizable": True,
            },
            {
                "id": "logo",
                "type": "image",
                "required": False,
                "position": {"top": 40, "right": 40, "width": 120, "height": 120},
                "style": {
                    "circle": True,
                    "background": "white",
                    "border": {
                        "width": "${customization.borders.width}",
                        "gradient": [
                            "${customization.colors.primary}",
                            "${customization.colors.secondary}",
                        ],
                    },
                },
                "fit": "contain",
                "zIndex": 5,
                "customizable": True,
            },
            {
                "id": "subtitles",
                "type": "text",
                "enabled": True,
                "position": {
                    "bottom": 220,
                    "centerX": True,
                    "width": 900,
                    "maxWidth": 900,
                },
                "style": {
                    "fontFamily": "${customization.fonts.primary.family}",
                    "fontSize": 80,
                    "fontWeight": "bold",
                    "color": "${customization.colors.text}",
                    "textAlign": "center",
                    "background": {
                        "color": "${customization.colors.overlay}",
                        "padding": 12,
                        "borderRadius": "${customization.borders.radius}",
                    },
                    "stroke": {"color": "black", "width": 1.5},
                    "lineHeight": 1.3,
                    "wordWrap": True,
                    "maxLines": 3,
                    "overflow": "ellipsis",
                },
                "animation": {
                    "in": {
                        "type": "${customization.animations.text_in.type}",
                        "duration": "${customization.animations.text_in.duration}",
                        "delay": 0.2,
                        "easing": "${customization.animations.text_in.easing}",
                    },
                    "out": {
                        "type": "${customization.animations.text_out.type}",
                        "duration": "${customization.animations.text_out.duration}",
                        "easing": "${customization.animations.text_out.easing}",
                    },
                },
                "zIndex": 6,
                "customizable": True,
            },
            {
                "id": "overlay",
                "type": "shape",
                "required": False,
                "shape": "rectangle",
                "position": "fill",
                "style": {"color": "rgba(0,0,0,0.3)", "opacity": 0.5},
                "zIndex": 1,
                "customizable": True,
            },
            {
                "id": "caption",
                "type": "text",
                "required": False,
                "enabled": False,
                "content": "",
                "position": {"top": 40, "left": 40, "width": 700, "maxWidth": 700},
                "style": {
                    "fontFamily": "${customization.fonts.secondary.family}",
                    "fontSize": 60,
                    "fontWeight": "bold",
                    "color": "${customization.colors.text}",
                    "textAlign": "left",
                    "background": {
                        "color": "${customization.colors.overlay}",
                        "padding": 10,
                        "borderRadius": "${customization.borders.radius}",
                    },
                    "lineHeight": 1.2,
                    "wordWrap": True,
                    "maxLines": 2,
                    "overflow": "ellipsis",
                },
                "animation": {
                    "in": {
                        "type": "slideIn",
                        "from": "left",
                        "duration": 0.5,
                        "delay": 0.3,
                        "easing": "easeOutQuad",
                    },
                    "out": {
                        "type": "slideOut",
                        "to": "left",
                        "duration": 0.5,
                        "easing": "easeInQuad",
                    },
                },
                "zIndex": 7,
                "customizable": True,
            },
        ],
    }
}
