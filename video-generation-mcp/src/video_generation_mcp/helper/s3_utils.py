import os
from video_generation_mcp.helper.s3_manager import S3Uploader


def read_folder_and_upload(video_id, s3_folder="videos"):
    """Upload specified files and folders to an S3 bucket and organize files in arrays by folder."""

    uploaded_objects = {}
    folders_to_scan = ["compressed"]
    files_to_upload = ["final.mp4", "thumbnail.jpg"]
    base_path = f"./temp/{video_id}"

    # Upload specified files
    for file in files_to_upload:
        local_file_path = os.path.join(base_path, file)
        s3_file_path = os.path.join(s3_folder)
        try:
            content_type = get_content_type(local_file_path)
            upload_path = S3Uploader().upload_file(
                s3_file_path,
                local_file_path,
                mimetype=content_type,
            )
            file_name_without_extension = os.path.splitext(file)[0]
            uploaded_objects[file_name_without_extension] = upload_path
        except FileNotFoundError:
            print(f"The file {local_file_path} was not found.")

    # Upload files in specified folders
    for root, dirs, files in os.walk(base_path):
        subfolder_name = os.path.basename(root)
        if subfolder_name in folders_to_scan:
            uploaded_objects[subfolder_name] = []
            for file in files:
                local_file_path = os.path.join(root, file)
                s3_file_path = os.path.join(s3_folder, subfolder_name)
                try:
                    content_type = get_content_type(local_file_path)
                    upload_path = S3Uploader().upload_file(
                        s3_file_path,
                        local_file_path,
                        mimetype=content_type,
                    )
                    uploaded_objects[subfolder_name].append(upload_path)
                except FileNotFoundError:
                    print(f"The file {local_file_path} was not found.")

    return uploaded_objects


def get_content_type(file_path: str) -> str:
    # Determine the content type based on the file extension
    if file_path.endswith(".mp4"):
        return "video/mp4"
    elif file_path.endswith(".jpg") or file_path.endswith(".jpeg"):
        return "image/jpeg"
    elif file_path.endswith(".png"):
        return "image/png"
    # Add more file types as needed
    return "application/octet-stream"  # Default content type
