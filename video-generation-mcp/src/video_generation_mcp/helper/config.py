import os
from dotenv import load_dotenv

from video_generation_mcp.helper.utils import create_dir

from ..loggers.logger import logger

from moviepy.config import change_settings


# Try to find the ImageMagick convert binary
def find_imagemagick_binary():
    possible_paths = [
        "/usr/bin/convert",
        "/usr/local/bin/convert",
        "/opt/homebrew/bin/convert",
    ]
    for path in possible_paths:
        if os.path.exists(path):
            return path
    raise IOError("ImageMagick binary cannot be found")


# Set the ImageMagick binary path before importing moviepy
os.environ["IMAGEMAGICK_BINARY"] = find_imagemagick_binary()

IMAGEMAGICK_BINARY: str = os.environ["IMAGEMAGICK_BINARY"]

change_settings({"IMAGEMAGICK_BINARY": IMAGEMAGICK_BINARY})

# change_settings({"FFMPEG_BINARY": "/usr/bin/ffmpeg"})


create_dir("./temp")

logger.info("Configuring environment variables...")


# Load environment variables from a .env file
load_dotenv()

# Access environment variables and store them in variables
DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")

HOST = os.getenv("HOST", "localhost")
PORT = int(os.getenv("PORT", 5004))

AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
S3_BUCKET_NAME = os.getenv("S3_BUCKET_NAME")
AWS_BASE_URL = os.getenv("AWS_BASE_URL")


# Add more variables as needed
# EXAMPLE_VAR = os.getenv('EXAMPLE_VAR')

# You can now use these variables throughout your project
