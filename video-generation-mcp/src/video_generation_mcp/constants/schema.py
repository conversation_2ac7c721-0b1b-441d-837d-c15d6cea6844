from pydantic import BaseModel, Field, HttpUrl, validator
from typing import List, Optional
from video_generation_mcp.constants.enum import VideoViewType

# Assuming this is your server's types module


class StockVideoClip(BaseModel):
    at_time: float = Field(..., ge=0)
    url: HttpUrl


class StockImageClip(BaseModel):
    at_time: float = Field(..., ge=0)
    url: HttpUrl


class EventStockClip(BaseModel):
    clip: int = Field(..., ge=0)
    at_time: float = Field(..., ge=0)
    duration: float = Field(..., gt=0)


class GenerateVideoObject(BaseModel):
    view_type: VideoViewType
    stock_video_clips: List[StockVideoClip] = []
    stock_image_clips: List[StockImageClip] = []
    event_stock_clips: List[EventStockClip] = []
    audio_urls: List[HttpUrl]
    avatar_video_urls: Optional[List[str]] = None
    subtitles: str = Field(..., min_length=1)

    @validator("audio_urls")
    def check_audio_urls(cls, v):
        if len(v) < 1:
            raise ValueError("audio_urls must contain at least one item")
        return v

    @validator("avatar_video_urls", each_item=True)
    def check_avatar_video_urls(cls, v):
        if v is not None and not v.isalnum():
            raise ValueError("Each avatar_video_url must be alphanumeric")
        return v


class GenerateVideo(BaseModel):
    video_data: GenerateVideoObject
