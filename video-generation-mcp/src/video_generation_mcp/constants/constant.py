class Descriptions:
    GENERATE_VIDEO = "generate and process the video"


MESSAGES = {
    "VIDEO_GENERATION_INPROGRESS": "The video generation process is currently underway",
    "VIDEO_GENERATION_DONE": "Video generation has been successfully completed",
    "VIDEO_UPLOAD_STARTED": "Video upload has been started successfully",
    "VIDEO_UPLOAD_INPROGRESS": "Video upload in progress",
    "STARTED_UPDATING_VIDEO_THUMBNAIL": "Started updating video thumbnail",
    "END_UPDATING_VIDEO_THUMBNAIL": "Video thumbnail has been uploaded",
    "VIDEO_UPLOAD_DONE": "Video has been uploaded successfully",
    "STARTED": "Video Creation Process Initiated",
    "DATA_RETRIEVAL_STARTED": "Started fetching video data",
    "DATA_RETRIEVED": "Video Data Successfully Retrieved",
    "START_URL_EXTRACTION": "Started extracting intro, outro and avatar video urls",
    "URLS_EXTRACTED": "Extracted Required URLs",
    "START_DOWNLOADING": "Downloading of Video Assets Started",
    "END_DOWNLOAD": "All Video Assets Downloaded",
    "START_UPLOADING": "Uploading Process Started",
    "UPLOADED": "Videos Combined and Uploaded",
    "DELETE_FILES": "Temporary Files Being Deleted",
    "UPDATE_VIDEO": "Video Status Being Updated",
    "VIDEO_CREATED": "Video Creation Process Completed",
    "SUBTITLE_SAVED": "Subtitle file created successfully",
}


# Template Names
VIDEO_TEMPLATES = {
    "PORTRAIT_TEMPLATE": "portrait_template",
    "LANDSCAPE_TEMPLATE": "landscape_template",
    "SQUARE_TEMPLATE": "square_template",
}


# Video Resoultions
VIDEO_RESOLUTIONS = {
    "LANDSCAPE": [
        (1280, 720),  # 720p (16:9)
        (854, 480),  # 480p (16:9)
        (640, 360),  # 360p (16:9)
        (426, 240),  # 240p (16:9)
    ],
    "PORTRAIT": [
        (720, 1280),  # 720x1280 (9:16)
        (480, 854),  # 480x854 (9:16)
        (360, 640),  # 360x640 (9:16)
        (240, 426),  # 240x426 (9:16)
    ],
    "SQUARE": [
        (720, 720),  # 720x720 (1:1)
        (480, 480),  # 480x480 (1:1)
        (360, 360),  # 360x360 (1:1)
        (240, 240),  # 240x240 (1:1)
    ],
}
