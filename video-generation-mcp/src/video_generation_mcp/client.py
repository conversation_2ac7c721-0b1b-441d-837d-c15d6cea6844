import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


async def main():
    # Set server parameters
    server_params = StdioServerParameters(
        command="video-generation-mcp",  # Path to server.py
        args=[
            "--directory",
            "video-generation-mcp",
            "run",
            "video-generation-mcp",
        ],  # Command line arguments (if needed)
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize session
            await session.initialize()

            try:
                # Get list of available tools
                tools = await session.list_tools()

                print(f"Available tools: {tools}")

                # Example of calling a tool
                tool_result = await session.call_tool(
                    "generate_video",
                    arguments={
                        "view_type": "PORTRAIT",
                        "stock_video_clips": [],
                        "stock_image_clips": [
                            {
                                "prompts": "A wide shot of a futuristic cityscape, featuring sleek skyscrapers with advanced technology integrated into their design, flying cars in the sky, and interconnected buildings with digital displays. The scene is set during the day with bright natural lighting, creating a sense of technological advancement and innovation. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic photography, futuristic urban landscape",
                                "at_time": 0,
                                "description": "Introduction to the technological leap with futuristic city visuals",
                                "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/d9bdede8-989c-4302-b75f-e7a65d33ccc0.jpg",
                            },
                            {
                                "prompts": "A medium shot of a robot in an office setting, performing various tasks with precision and efficiency. The robot is humanoid, with sleek metallic features. The office is modern, with digital interfaces and holographic displays. Soft artificial lighting creates a professional atmosphere. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic photography, robotics in a workplace",
                                "at_time": 4.16,
                                "description": "Depiction of AGI in action across different sectors",
                                "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/e7cb0b21-b59a-49a3-846a-d86995f3e4d0.jpg",
                            },
                            {
                                "prompts": "A close-up shot of a surgeon using advanced robotic arms during a surgery. The operating room is high-tech, with digital screens and robotic equipment. The surgeon is focused, wearing a surgical mask and gloves. Bright, focused lighting highlights the precision of the procedure. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic photography, medical technology",
                                "at_time": 11.352,
                                "description": "Highlighting AGI's role in surgery",
                                "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/0d7647d9-e888-4a2a-aa65-4d25eb3c9f03.jpg",
                            },
                            {
                                "prompts": "A wide shot of an interactive classroom where students are engaged with educational technology. The classroom is modern, with digital boards and students wearing augmented reality glasses. Natural daylight fills the room, creating an immersive learning environment. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic photography, futuristic education",
                                "at_time": 20,
                                "description": "Students interacting with personalized educational technology",
                                "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/a1f3553c-0498-46c6-b9ce-8e32703f84ff.jpg",
                            },
                            {
                                "prompts": "A dynamic aerial shot of a bustling cityscape, showcasing automated industrial assembly lines and smart city technologies optimizing traffic and energy use. The city is vibrant, with electric public transports and green architecture. Evening lighting with city lights creates a lively atmosphere. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic photography, smart city transformation",
                                "at_time": 30.378,
                                "description": "AGI's influence on industry efficiency and urban planning",
                                "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/4a4ce570-4acc-40c1-9710-9feec0487627.jpg",
                            },
                            {
                                "prompts": "A medium shot of a diverse group of professionals in a corporate meeting discussing ethical implications of technology. The meeting room is modern, with digital screens displaying data. Warm artificial lighting creates an atmosphere of contemplation and discussion. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic photography, corporate ethics discussion",
                                "at_time": 44.538,
                                "description": "Reflective tone on technological impact on society",
                                "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/92d3b968-dfad-4418-bc11-566026a8a974.jpg",
                            },
                            {
                                "prompts": "A wide shot of a diverse group of innovators planning futuristic projects, with futuristic technologies and landscapes in the background. The setting is inspirational, with cutting-edge tech and diverse individuals collaborating. Bright, optimistic lighting symbolizes potential and togetherness. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic photography, future innovation",
                                "at_time": 58.868,
                                "description": "Concluding with a motivational appeal to potential future possibilities",
                                "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/f8425336-bce3-4428-bd18-88f4e2891221.jpg",
                            },
                        ],
                        "event_stock_clips": [],
                        "audio_urls": [
                            "https://peregrine-results.s3.amazonaws.com/dwUL8lLMZWvnzlrWOx.mp3"
                        ],
                        "subtitles": "",
                    },
                )
                print(f"Tool execution result: {tool_result}")

            except Exception as e:
                print(f"An error occurred: {e}")


if __name__ == "__main__":
    print("Running")
    asyncio.run(main())
