"""
<PERSON><PERSON>t to run all tests for the MCP server.
"""
import asyncio
import subprocess
import time
import sys
import os
import signal
from pathlib import Path


def start_server():
    """Start the MCP server in a separate process."""
    print("Starting MCP server...")
    server_process = subprocess.Popen(
        [sys.executable, "-m", "script_generator_server"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
    )
    
    # Wait for the server to start
    time.sleep(5)
    
    return server_process


def run_test(test_file):
    """Run a test file."""
    print(f"\nRunning test: {test_file}")
    result = subprocess.run(
        [sys.executable, "-m", "pytest", test_file, "-v"],
        capture_output=True,
        text=True,
    )
    
    print(result.stdout)
    if result.stderr:
        print(f"Errors: {result.stderr}")
    
    return result.returncode == 0


def run_client_test(connection_type, use_auth=False):
    """Run the client test with the specified connection type."""
    print(f"\nRunning client test with {connection_type} connection")
    
    cmd = [sys.executable, "client.py", "--connection-type", connection_type]
    if use_auth:
        cmd.append("--auth")
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    print(result.stdout)
    if result.stderr:
        print(f"Errors: {result.stderr}")
    
    return result.returncode == 0


def main():
    """Main function to run all tests."""
    # Create the tests directory if it doesn't exist
    Path("tests").mkdir(exist_ok=True)
    
    # Start the server
    server_process = start_server()
    
    try:
        # Run the tests
        test_files = [
            "tests/test_stdio_connection.py",
            "tests/test_sse_connection.py",
            "tests/test_streamable_http_connection.py",
        ]
        
        all_tests_passed = True
        for test_file in test_files:
            if not run_test(test_file):
                all_tests_passed = False
        
        # Run client tests
        connection_types = ["stdio", "sse", "streamable-http"]
        for conn_type in connection_types:
            # Test without authentication
            if not run_client_test(conn_type):
                all_tests_passed = False
            
            # Test with authentication (except for stdio)
            if conn_type != "stdio":
                if not run_client_test(conn_type, use_auth=True):
                    all_tests_passed = False
        
        if all_tests_passed:
            print("\n✅ All tests passed!")
        else:
            print("\n❌ Some tests failed!")
    
    finally:
        # Stop the server
        print("\nStopping MCP server...")
        server_process.terminate()
        try:
            server_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            server_process.kill()


if __name__ == "__main__":
    main()
