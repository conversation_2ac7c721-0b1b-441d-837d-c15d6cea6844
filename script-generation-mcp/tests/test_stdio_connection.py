"""
Test for standard I/O connection to the MCP server.
"""

import asyncio
import pytest
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


@pytest.mark.asyncio
async def test_stdio_connection():
    """Test standard I/O connection to the MCP server."""
    # Set server parameters
    server_params = StdioServerParameters(
        command="python",  # Path to server.py
        args=["-m", "script_generator_server"],  # Command line arguments
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize session
            await session.initialize()

            # Get list of available tools
            tools = await session.list_tools()

            # Verify that the script_generate tool is available
            assert any(tool.name == "script_generate" for tool in tools)

            # Call the script_generate tool
            result = await session.call_tool(
                "script_generate",
                arguments={
                    "topic": "Test Topic",
                    "script_type": "TOPIC",
                    "keywords": {
                        "time": "2 minutes",
                        "tone": "informative",
                        "audience": "general",
                    },
                    "video_type": "SHORT",
                },
            )

            # Verify that the result contains a script
            assert result is not None
            assert isinstance(result, list)
            assert len(result) > 0

            # Parse the JSON content
            content = result[0].text
            assert "script" in content
            assert "title" in content


if __name__ == "__main__":
    asyncio.run(test_stdio_connection())
