"""
Test for SSE connection to the MCP server.
"""
import asyncio
import pytest
import httpx
from mcp import ClientSession
from mcp.client.sse import sse_client


@pytest.mark.asyncio
async def test_sse_connection():
    """Test SSE connection to the MCP server."""
    # Start the server in a separate process
    # This assumes the server is already running on localhost:5004
    
    server_url = "http://localhost:5004"
    
    async with sse_client(f"{server_url}/sse", f"{server_url}/script") as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize session
            await session.initialize()
            
            # Get list of available tools
            tools = await session.list_tools()
            
            # Verify that the script_generate tool is available
            assert any(tool.name == "script_generate" for tool in tools)
            
            # Call the script_generate tool
            result = await session.call_tool(
                "script_generate",
                arguments={
                    "topic": "SSE Test Topic",
                    "script_type": "TOPIC",
                    "keywords": {
                        "time": "2 minutes",
                        "tone": "informative",
                        "audience": "general"
                    },
                    "video_type": "SHORT",
                },
            )
            
            # Verify that the result contains a script
            assert result is not None
            assert isinstance(result, list)
            assert len(result) > 0
            
            # Parse the JSON content
            content = result[0].text
            assert "script" in content
            assert "title" in content


@pytest.mark.asyncio
async def test_sse_authentication():
    """Test SSE connection with authentication."""
    server_url = "http://localhost:5004"
    
    # Register a client
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{server_url}/oauth/register",
            json={"scopes": ["script_generate"]},
        )
        assert response.status_code == 200
        client_data = response.json()
        
        # Get a token
        response = await client.post(
            f"{server_url}/oauth/token",
            json={
                "client_id": client_data["client_id"],
                "client_secret": client_data["client_secret"],
                "scopes": ["script_generate"],
            },
        )
        assert response.status_code == 200
        token_data = response.json()
        
        # Use the token for SSE connection
        headers = {"Authorization": f"Bearer {token_data['access_token']}"}
        
        async with sse_client(
            f"{server_url}/sse", 
            f"{server_url}/script", 
            headers=headers
        ) as (read, write):
            async with ClientSession(read, write) as session:
                # Initialize session
                await session.initialize()
                
                # Get list of available tools
                tools = await session.list_tools()
                
                # Verify that the script_generate tool is available
                assert any(tool.name == "script_generate" for tool in tools)


if __name__ == "__main__":
    asyncio.run(test_sse_connection())
