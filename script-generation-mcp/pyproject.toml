[project]
name = "script-generator-server"
version = "0.1.0"
description = "A MCP server project"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "tavily-python>=0.5.1,<0.6.0",
    "autogen-ext[openai]>=0.4.9,<0.5.0",
    "autogen-core>=0.4.9,<0.5.0",
    "langchain-openai>=0.3.8",
    "beautifulsoup4>=4.13.3",
    "fake-useragent>=2.1.0",
    "langchain-community>=0.3.19",
    "autogen-agentchat>=*******",
    "pyautogen==0.2.28",
    "mcp>=1.8.1",
    "numpy>=1.26.4",
    "pytest>=8.3.5",
    "fastapi>=0.115.12",
    "aiohttp>=3.11.0,<3.11.14",          # Pin to a version before the yanked one
    "httpx>=0.27.0",
    "pytest-asyncio>=0.21.1",
    "mcp-proxy>=0.7.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=8.0.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "pytest-env>=1.1.3",
    "pytest-xdist>=3.5.0",    # For parallel testing

    # HTTP Testing
    "httpx>=0.24.1",
    "requests-mock>=1.11.0",

    # Code Quality
    "black>=24.1.0", # Code formatting
    "isort>=5.13.0", # Import sorting
    "flake8>=7.0.0", # Code linting
    "mypy>=1.8.0",   # Type checking

    # Documentation
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=2.0.0",

    # Development Tools
    "pre-commit>=3.6.0", # Git hooks
    "tox>=4.12.0",       # Test automation
]

test = [
    "pytest>=8.0.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "httpx>=0.24.1",
]

[[project.authors]]
name = "AkashAaglawe22012"
email = "<EMAIL>"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
script-generator-server = "script_generator_server:main"

[tool.pytest.ini_options]
pythonpath = ["src"]
testpaths = ["src/script_generator_server/tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v -ra -q"

[tool.coverage.run]
source = ["src/script_generator_server"]
omit = ["tests/*", "**/__init__.py"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError",
]

[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

[tool.tox]
legacy_tox_ini = """
[tox]
envlist = py313
isolated_build = True

[testenv]
deps =
    .[test]
commands =
    pytest {posargs:tests}
"""
