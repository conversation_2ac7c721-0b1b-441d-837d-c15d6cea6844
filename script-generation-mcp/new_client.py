import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


async def main():
    # Set server parameters
    server_params = StdioServerParameters(
        command="script-generator-server",  # Path to server.py
        args=[
            "C://Users//INDIA//Desktop//mcp//content-extractor-mcp-MCP-server//script_generator_server//src//script_generator_server//server.py",
            "script-generator-server",
            "run",
            "script-generator-server",
        ],  # Command line arguments (if needed)
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize session
            await session.initialize()

            try:
                # Get list of available tools
                tools = await session.list_tools()

                print(f"Available tools: {tools}")

                # Example of calling a tool
                tool_result = await session.call_tool(
                    "script-generator-server",
                    arguments={
                        "topic": "AI",
                        "keywords": "CV",
                        # The McLaren Senna takes automotive engineering to extraordinary heights, achieving a top speed of 208 mph, powered by a twin-turbocharged 4.0-liter V8 engine producing a staggering 789 horsepower. This perfect blend of aerodynamics and power yields an unparalleled driving experience. Transition through sleek curves and assertive lines, pairing function with jaw-dropping form.
                        # Visualize B-roll footage: the Senna tearing through a racetrack, wind-tunnel tests showcasing its aerodynamic prowess, and interior shots highlighting the state-of-the-art cockpit design. Key statistics like "208 mph" flash on-screen for emphasis.
                        # Are you ready to experience the ultimate expression of control and precision? Reflect on the prowess of F1 innovation reincarnated into a road car. The McLaren Senna isn't just a car—it's a revolution in engineering, designed to challenge the limits of possibility.
                        # As we close, consider: Are you ready to step into a future defined by McLaren ingenuity? The legacy of Senna lives on—fueled by technology, driven by passion. Explore the limits. Unleash the McLaren Senna."""
                    },
                )
                print(f"Tool execution result: {tool_result}")

            except Exception as e:
                print(f"An error occurred: {e}")


if __name__ == "__main__":
    print("Running")
    asyncio.run(main())
