import asyncio
import contextlib
import json

import anyio
import mcp.server.stdio
import mcp.types as types
import uvicorn
from mcp.server import NotificationOptions, Server
from mcp.server.auth.settings import AuthSettings, ClientRegistrationOptions
from mcp.server.models import InitializationOptions
from mcp.server.sse import SseServerTransport
from mcp.server.streamable_http_manager import StreamableHTTPSessionManager
from starlette.applications import Starlette
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware
from starlette.routing import Route

from script_generator_server.constants.constant import Descriptions
from script_generator_server.constants.enum import Tools
from script_generator_server.constants.schema import GenerateScriptInput, ResearchInput

# from script_generator_server.auth_provider import auth_provider
from script_generator_server.event_store import InMemoryEventStore
from script_generator_server.helper.config import HOST, PORT
from script_generator_server.loggers.logger import logger

from .constants.enum import ScriptType
from .helper.tav import search_tool
from .services.script import generate_script

# Store notes as a simple key-value dict to demonstrate state management
notes: dict[str, str] = {}

server = Server("script-generator-server")


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """
    List available tools.
    Each tool specifies its arguments using JSON Schema validation.
    """
    return [
        types.Tool(
            name="script_generate",
            description="Provide topic and keyword to generator Script",
            inputSchema=GenerateScriptInput.model_json_schema(),
        ),
        types.Tool(
            name=Tools.RESEARCH,
            description=Descriptions.RESEARCH,
            inputSchema=ResearchInput.model_json_schema(),
        ),
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict | None) -> types.CallToolResult:
    """
    Handle tool execution requests.
    Tools can modify server state and notify clients of changes.
    """
    try:
        match name:
            case Tools.GENERATE_SCRIPT:
                topic = arguments.get("topic")
                script_type = ScriptType.TOPIC
                keywords = arguments.get("keywords")
                video_type = arguments.get("video_type").upper()
                link = arguments.get("link")

                result = await generate_script(
                    topic,
                    script_type,
                    keywords,
                    video_type,
                    link,
                )

                # result = {
                #     "title": "latest nvidia event updates",
                #     "script": """Missed Nvidia's GTC 2025? Here's your quick recap! In just a minute, let's dive into what Nvidia's latest event revealed about the future of technology.You won't believe what went down at the GPU Technology Conference 2025. Nvidia's making waves with some cutting-edge advancements in AI, robotics, and high-performance computing. These breakthroughs are pushing us right into the tech of tomorrow.First—the GROOT AI model is your new robotic bestie, equipped with lightning-fast reflexes and decision-making that matches human capabilities. And the Blackwell Ultra GPU series is grabbing headlines as the fastest AI hardware yet, bringing significant power to AI and computational tasks. Let's not forget about their Spectrum-X Ethernet Switch delivering speeds of 1.6 terabits per second per port.With these moves, Nvidia's reshaping tech ecosystems, drawing out a future where everything's more efficient, on a massive scale, with limitless innovation.Want to know more about what's coming next? Hit that like button if you're as excited as I am, and make sure to subscribe for the latest in tech.So there you have it—how Nvidia's lighting the way for a smarter future. Don’t miss out, like and subscribe for more insider info!""",
                #     "script_type": "TOPIC",
                #     "video_type": "SHORT",
                # }

                print("result= ", result)
            case Tools.RESEARCH:
                topic = arguments.get("topic")

                data = search_tool(topic)

                result = {"content": data}

                print("result= ", result)
            case _:
                return [types.TextContent(type="text", text=f"Unknown tool: {name}")]

    except Exception as error:
        print("Error:", error)
        # return types.CallToolResult(
        #     isError=True,
        #     content=[types.TextContent(type="text", text=f"Error: {str(error)}")],
        # )
        error = {"message": f"Error: {str(error)}", "is_error": True}
        return [types.TextContent(type="text", text=json.dumps(error, indent=2))]
    # return types.CallToolResult(
    #     content=[types.TextContent(type="text", text=json.dumps(result, indent=2))]
    # )
    return [types.TextContent(type="text", text=json.dumps(result, indent=2))]


async def main():
    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="script-generator-server",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )


async def create_app():
    # # Create auth settings
    # auth_settings = AuthSettings(
    #     issuer_url="https://script-generator-server.example.com",
    #     client_registration_options=ClientRegistrationOptions(
    #         enabled=True,
    #         valid_scopes=["script_generate"],
    #         default_scopes=["script_generate"],
    #     ),
    #     required_scopes=["script_generate"],
    # )

    sse = SseServerTransport("/script")

    # Create an event store for resumability
    event_store = InMemoryEventStore(max_events_per_stream=100)

    # Create the session manager with the event store
    try:
        # Try with auth parameters (newer MCP versions)
        session_manager = StreamableHTTPSessionManager(
            app=server,
            event_store=event_store,  # Use our event store for resumability
            json_response=False,  # Use SSE format for responses
            stateless=False,  # Stateful mode for better user experience
            # auth_server_provider=auth_provider,
            # auth_settings=auth_settings,
        )
        logger.info(
            "StreamableHTTPSessionManager initialized with authentication support"
        )
    except TypeError:
        # Fallback for older MCP versions that don't support auth
        logger.warning(
            "Your MCP version doesn't support authentication in StreamableHTTPSessionManager"
        )
        logger.warning(
            "Initializing StreamableHTTPSessionManager without authentication"
        )

        # Try with just the basic parameters
        try:
            session_manager = StreamableHTTPSessionManager(
                app=server,
                event_store=event_store,
                json_response=False,
            )
            logger.info(
                "StreamableHTTPSessionManager initialized without authentication"
            )
        except TypeError:
            # If that still fails, try with minimal parameters
            logger.warning(
                "Falling back to minimal StreamableHTTPSessionManager initialization"
            )
            session_manager = StreamableHTTPSessionManager(app=server)
    except Exception as e:
        logger.error(f"Failed to initialize StreamableHTTPSessionManager: {e}")
        session_manager = None

    class HandleSSE:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            try:
                logger.info("Handling SSE connection ....")

                # # Manual authentication check for older MCP versions
                # # ASGI headers are a list of tuples [(b'key', b'value'), ...]
                # headers = dict(scope.get("headers", []))
                # if b"authorization" in headers:
                #     auth_header = headers[b"authorization"].decode("utf-8")
                #     if auth_header.startswith("Bearer "):
                #         token = auth_header[7:]  # Remove "Bearer " prefix
                #         if not auth_provider.validate_token(token):
                #             # Return 401 Unauthorized
                #             await send(
                #                 {
                #                     "type": "http.response.start",
                #                     "status": 401,
                #                     "headers": [(b"content-type", b"application/json")],
                #                 }
                #             )
                #             await send(
                #                 {
                #                     "type": "http.response.body",
                #                     "body": json.dumps(
                #                         {"error": "Unauthorized"}
                #                     ).encode("utf-8"),
                #                 }
                #             )
                #             return

                # Continue with SSE connection
                async with self.sse.connect_sse(scope, receive, send) as streams:
                    await server.run(
                        streams[0],
                        streams[1],
                        server.create_initialization_options(),
                    )

                logger.info("SSE connection closed ....")

            except anyio.BrokenResourceError:
                logger.error("SSE connection broken - client likely disconnected")

    class HandleMessages:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            try:
                # # Manual authentication check for older MCP versions
                # # ASGI headers are a list of tuples [(b'key', b'value'), ...]
                # headers = dict(scope.get("headers", []))
                # if b"authorization" in headers:
                #     auth_header = headers[b"authorization"].decode("utf-8")
                #     if auth_header.startswith("Bearer "):
                #         token = auth_header[7:]  # Remove "Bearer " prefix
                #         if not auth_provider.validate_token(token):
                #             # Return 401 Unauthorized
                #             await send(
                #                 {
                #                     "type": "http.response.start",
                #                     "status": 401,
                #                     "headers": [(b"content-type", b"application/json")],
                #                 }
                #             )
                #             await send(
                #                 {
                #                     "type": "http.response.body",
                #                     "body": json.dumps(
                #                         {"error": "Unauthorized"}
                #                     ).encode("utf-8"),
                #                 }
                #             )
                #             return

                # Continue with standard message handling
                await self.sse.handle_post_message(scope, receive, send)

            except Exception as e:
                logger.error(f"Error handling POST message: {e}")
                # Return a 500 error
                await send(
                    {
                        "type": "http.response.start",
                        "status": 500,
                        "headers": [(b"content-type", b"application/json")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": json.dumps({"error": str(e)}).encode("utf-8"),
                    }
                )

    # Create a class for handling streamable HTTP connections
    class HandleStreamableHttp:
        def __init__(self, session_manager):
            self.session_manager = session_manager

        async def __call__(self, scope, receive, send):
            if self.session_manager is not None:
                try:
                    logger.info("Handling Streamable HTTP connection ....")
                    await self.session_manager.handle_request(scope, receive, send)
                    logger.info("Streamable HTTP connection closed ....")
                except Exception as e:
                    logger.error(f"Error handling Streamable HTTP request: {e}")
            else:
                # Return a 501 Not Implemented response if streamable HTTP is not available
                await send(
                    {
                        "type": "http.response.start",
                        "status": 501,
                        "headers": [(b"content-type", b"application/json")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": json.dumps(
                            {"error": "Streamable HTTP transport is not available"}
                        ).encode("utf-8"),
                    }
                )

    # Simple OAuth handlers as classes
    class HandleRegisterClient:
        """Handle client registration requests."""

        async def __call__(self, scope, receive, send):
            try:
                # Get request body
                body = await get_request_body(receive)
                data = json.loads(body)

                # Register client
                result = auth_provider.register_new_client(data.get("scopes"))

                # Return response
                await send_json_response(send, 200, result)
            except Exception as e:
                logger.error(f"Error handling client registration: {e}")
                await send_json_response(send, 500, {"error": str(e)})

    class HandleTokenRequest:
        """Handle token requests."""

        async def __call__(self, scope, receive, send):
            try:
                # Get request body
                body = await get_request_body(receive)
                data = json.loads(body)

                # Create token
                result = auth_provider.create_direct_token(
                    data.get("client_id"), data.get("client_secret"), data.get("scopes")
                )

                # Return response
                await send_json_response(send, 200, result)
            except Exception as e:
                logger.error(f"Error handling token request: {e}")
                await send_json_response(send, 500, {"error": str(e)})

    class HandleTokenRevocation:
        """Handle token revocation requests."""

        async def __call__(self, scope, receive, send):
            try:
                # Get request body
                body = await get_request_body(receive)
                data = json.loads(body)

                # Revoke token
                await auth_provider.revoke_token(data.get("token"))

                # Return response
                await send_json_response(send, 200, {"status": "success"})
            except Exception as e:
                logger.error(f"Error handling token revocation: {e}")
                await send_json_response(send, 500, {"error": str(e)})

    # Helper functions for OAuth handlers
    async def get_request_body(receive):
        """Get request body from ASGI receive function."""
        body = b""
        more_body = True

        while more_body:
            message = await receive()
            body += message.get("body", b"")
            more_body = message.get("more_body", False)

        return body.decode("utf-8")

    async def send_json_response(send, status, data):
        """Send JSON response."""
        await send(
            {
                "type": "http.response.start",
                "status": status,
                "headers": [(b"content-type", b"application/json")],
            }
        )
        await send(
            {
                "type": "http.response.body",
                "body": json.dumps(data).encode("utf-8"),
            }
        )

    # Define routes
    routes = [
        # SSE transport routes
        Route("/sse", endpoint=HandleSSE(sse), methods=["GET"]),
        Route("/script", endpoint=HandleMessages(sse), methods=["POST"]),
        # OAuth routes
        # Route("/oauth/register", endpoint=HandleRegisterClient(), methods=["POST"]),
        # Route("/oauth/token", endpoint=HandleTokenRequest(), methods=["POST"]),
        # Route("/oauth/revoke", endpoint=HandleTokenRevocation(), methods=["POST"]),
    ]

    # Add Streamable HTTP route if available
    if session_manager is not None:
        routes.append(
            Route(
                "/mcp", endpoint=HandleStreamableHttp(session_manager), methods=["POST"]
            )
        )

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
        )
    ]

    # Define lifespan for session manager
    @contextlib.asynccontextmanager
    async def lifespan(app):
        """Context manager for session manager."""
        if session_manager is not None:
            async with session_manager.run():
                logger.info("Application started with StreamableHTTP session manager!")
                try:
                    yield
                finally:
                    logger.info("Application shutting down...")
        else:
            # No session manager, just yield
            yield

    return Starlette(routes=routes, middleware=middleware, lifespan=lifespan)


async def start_server():
    """Start the server asynchronously."""
    app = await create_app()
    logger.info(f"Starting server at {HOST}:{PORT}")

    # Use uvicorn's async API
    config = uvicorn.Config(app, host=HOST, port=PORT)
    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    while True:
        try:
            # Use asyncio.run to run the async start_server function
            asyncio.run(start_server())
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
            break
        except Exception as e:
            logger.error(f"Server crashed with error: {e}")
            continue
