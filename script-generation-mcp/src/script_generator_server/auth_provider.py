"""
Authentication provider for the MCP server.
Based on the GitHub OAuth example but simplified for direct token usage.
"""

import logging
import secrets
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pydantic import AnyHttpUrl

from mcp.server.auth.provider import (
    AccessToken,
    AuthorizationCode,
    AuthorizationParams,
    OAuthAuthorizationServerProvider,
    RefreshToken,
)
from mcp.shared.auth import OAuthClientInformationFull, OAuthToken

logger = logging.getLogger(__name__)


@dataclass
class Client:
    client_id: str
    client_secret: str
    scopes: List[str]


@dataclass
class Token:
    access_token: str
    client_id: str
    scopes: List[str]
    expires_at: Optional[int] = None  # Unix timestamp


class ScriptGeneratorOAuthProvider(OAuthAuthorizationServerProvider):
    """
    OAuth provider for the script generator server.
    Simplified version that doesn't require external OAuth service.
    """

    def __init__(self):
        self.clients: Dict[str, OAuthClientInformationFull] = {}
        self.auth_codes: Dict[str, AuthorizationCode] = {}
        self.tokens: Dict[str, AccessToken] = {}
        self.state_mapping: Dict[str, Dict[str, str]] = {}

    async def get_client(self, client_id: str) -> Optional[OAuthClientInformationFull]:
        """Get OAuth client information."""
        return self.clients.get(client_id)

    async def register_client(self, client_info: OAuthClientInformationFull):
        """Register a new OAuth client."""
        self.clients[client_info.client_id] = client_info

    async def authorize(
        self, client: OAuthClientInformationFull, params: AuthorizationParams
    ) -> str:
        """Generate an authorization URL for direct authorization flow."""
        state = params.state or secrets.token_hex(16)

        # Store the state mapping
        self.state_mapping[state] = {
            "redirect_uri": str(params.redirect_uri),
            "code_challenge": params.code_challenge,
            "redirect_uri_provided_explicitly": str(
                params.redirect_uri_provided_explicitly
            ),
            "client_id": client.client_id,
        }

        # Create authorization code directly
        code = f"auth_{secrets.token_hex(16)}"

        # Store the authorization code
        auth_code = AuthorizationCode(
            code=code,
            client_id=client.client_id,
            redirect_uri=params.redirect_uri,
            redirect_uri_provided_explicitly=params.redirect_uri_provided_explicitly,
            expires_at=time.time() + 300,  # 5 minutes
            scopes=client.scopes,
            code_challenge=params.code_challenge,
        )
        self.auth_codes[code] = auth_code

        # Construct redirect URI with code and state
        from mcp.server.auth.provider import construct_redirect_uri

        redirect_uri = construct_redirect_uri(
            str(params.redirect_uri), code=code, state=state
        )

        return redirect_uri

    async def load_authorization_code(
        self, client: OAuthClientInformationFull, authorization_code: str
    ) -> Optional[AuthorizationCode]:
        """Load an authorization code."""
        return self.auth_codes.get(authorization_code)

    async def exchange_authorization_code(
        self, client: OAuthClientInformationFull, authorization_code: AuthorizationCode
    ) -> OAuthToken:
        """Exchange authorization code for tokens."""
        if authorization_code.code not in self.auth_codes:
            raise ValueError("Invalid authorization code")

        # Generate access token
        access_token = f"token_{secrets.token_hex(32)}"
        expires_in = 3600  # 1 hour

        # Store token
        self.tokens[access_token] = AccessToken(
            token=access_token,
            client_id=client.client_id,
            scopes=authorization_code.scopes,
            expires_at=int(time.time()) + expires_in,
        )

        # Remove used authorization code
        del self.auth_codes[authorization_code.code]

        return OAuthToken(
            access_token=access_token,
            token_type="bearer",
            expires_in=expires_in,
            scope=" ".join(authorization_code.scopes),
        )

    async def load_access_token(self, token: str) -> Optional[AccessToken]:
        """Load and validate an access token."""
        access_token = self.tokens.get(token)
        if not access_token:
            return None

        # Check if expired
        if access_token.expires_at and access_token.expires_at < time.time():
            del self.tokens[token]
            return None

        return access_token

    async def load_refresh_token(
        self, client: OAuthClientInformationFull, refresh_token: str
    ) -> Optional[RefreshToken]:
        """Load a refresh token - not supported."""
        return None

    async def exchange_refresh_token(
        self,
        client: OAuthClientInformationFull,
        refresh_token: RefreshToken,
        scopes: List[str],
    ) -> OAuthToken:
        """Exchange refresh token - not supported."""
        raise NotImplementedError("Refresh tokens are not supported")

    async def revoke_token(
        self, token: str, token_type_hint: Optional[str] = None
    ) -> None:
        """Revoke a token."""
        if token in self.tokens:
            del self.tokens[token]

    # Direct client registration method for simplified usage
    def register_new_client(self, scopes: Optional[List[str]] = None) -> Dict[str, Any]:
        """Register a new client and return client credentials."""
        client_id = f"client_{secrets.token_hex(8)}"
        client_secret = secrets.token_hex(16)

        # Use requested scopes or default to script_generate
        client_scopes = scopes if scopes else ["script_generate"]

        # Create client information
        # Different versions of MCP might have different field names
        try:
            # Try with scopes field
            client_info = OAuthClientInformationFull(
                client_id=client_id,
                client_secret=client_secret,
                redirect_uris=[
                    "http://localhost:8000/callback"
                ],  # Need at least one redirect URI
                scopes=client_scopes,
            )
        except TypeError:
            # Try with scope field (singular)
            try:
                client_info = OAuthClientInformationFull(
                    client_id=client_id,
                    client_secret=client_secret,
                    redirect_uris=["http://localhost:8000/callback"],
                    scope=" ".join(client_scopes),
                )
            except TypeError:
                # If both fail, create a minimal client
                client_info = OAuthClientInformationFull(
                    client_id=client_id,
                    client_secret=client_secret,
                    redirect_uris=["http://localhost:8000/callback"],
                )

        # Register client
        self.clients[client_id] = client_info

        return {
            "client_id": client_id,
            "client_secret": client_secret,
            "scopes": client_scopes,
        }

    # Direct token creation method for simplified usage
    def create_direct_token(
        self, client_id: str, client_secret: str, scopes: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Create a token directly without authorization flow."""
        # Validate client credentials
        client = self.clients.get(client_id)
        if not client or client.client_secret != client_secret:
            raise ValueError("Invalid client credentials")

        # Generate access token
        access_token = f"token_{secrets.token_hex(32)}"
        expires_in = 3600  # 1 hour

        # Get client scopes based on the available attributes
        client_scopes = []
        if hasattr(client, "scopes"):
            client_scopes = client.scopes
        elif hasattr(client, "scope"):
            # If scope is a string, split it
            if isinstance(client.scope, str):
                client_scopes = client.scope.split()
            else:
                client_scopes = client.scope
        else:
            # Default scopes if none are available
            client_scopes = ["script_generate"]

        # Use requested scopes or default to client's scopes
        token_scopes = scopes if scopes else client_scopes

        # Ensure requested scopes are a subset of client's scopes
        # Make sure both lists are not None
        if client_scopes and token_scopes:
            if not all(scope in client_scopes for scope in token_scopes):
                raise ValueError("Requested scopes exceed client's authorized scopes")
        elif not client_scopes:
            # If client has no scopes, use the requested scopes
            client_scopes = token_scopes or ["script_generate"]
        elif not token_scopes:
            # If no token scopes requested, use client scopes
            token_scopes = client_scopes

        # Store token
        self.tokens[access_token] = AccessToken(
            token=access_token,
            client_id=client_id,
            scopes=token_scopes,
            expires_at=int(time.time()) + expires_in,
        )

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": expires_in,
            "scope": " ".join(token_scopes),
        }

    # Simple token validation method for direct usage
    def validate_token(self, token: str) -> bool:
        """Validate an access token."""
        # Check if token exists
        access_token = self.tokens.get(token)
        if not access_token:
            return False

        # Check if token is expired
        if access_token.expires_at and access_token.expires_at < time.time():
            del self.tokens[token]
            return False

        return True


# Create a singleton instance
auth_provider = ScriptGeneratorOAuthProvider()
