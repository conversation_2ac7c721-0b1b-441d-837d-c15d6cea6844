import pytest
from script_generator_server.services.script_generator import ScriptGenerator
from script_generator_server.exceptions import ScriptGenerationError


def test_script_generator_initialization():
    """Test ScriptGenerator initialization"""
    generator = ScriptGenerator()
    assert generator is not None
    assert hasattr(generator, "generate_script")


def test_generate_script_success(mock_script_generator, sample_script_request):
    """Test successful script generation"""
    result = mock_script_generator.generate_script(sample_script_request)
    assert result is not None
    assert "content" in result
    assert "duration" in result
    assert result["duration"] == sample_script_request["duration"]


def test_generate_script_with_invalid_duration(mock_script_generator):
    """Test script generation with invalid duration"""
    invalid_request = {"topic": "Test Topic", "duration": -1}
    with pytest.raises(ScriptGenerationError):
        mock_script_generator.generate_script(invalid_request)


def test_script_formatting(mock_script_generator, sample_script_request):
    """Test script formatting"""
    result = mock_script_generator.generate_script(sample_script_request)
    assert isinstance(result["content"], str)
    assert len(result["content"]) > 0
    assert result["word_count"] > 0


@pytest.mark.parametrize("tone", ["professional", "casual", "humorous"])
def test_script_generation_different_tones(
    mock_script_generator, sample_script_request
):
    """Test script generation with different tones"""
    sample_script_request["tone"] = tone
    result = mock_script_generator.generate_script(sample_script_request)
    assert result is not None
    assert "content" in result
