import pytest
from fastapi import status
from script_generator_server.exceptions import (
    ScriptGenerationError,
    InvalidRequestError,
)


def test_script_generation_error_handling(test_client, sample_script_request):
    """Test error handling during script generation"""
    # Modify request to trigger an error
    sample_script_request["duration"] = 0
    response = test_client.post("/generate", json=sample_script_request)
    assert response.status_code == status.HTTP_400_BAD_REQUEST


def test_invalid_request_error_handling(test_client):
    """Test handling of invalid requests"""
    invalid_request = {}  # Empty request
    response = test_client.post("/generate", json=invalid_request)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


def test_rate_limiting(test_client, sample_script_request):
    """Test rate limiting functionality"""
    # Make multiple requests in quick succession
    responses = [
        test_client.post("/generate", json=sample_script_request) for _ in range(5)
    ]
    # Check if any request was rate limited
    assert any(r.status_code == status.HTTP_429_TOO_MANY_REQUESTS for r in responses)


def test_invalid_content_type(test_client):
    """Test handling of invalid content type"""
    response = test_client.post(
        "/generate", data="not json data", headers={"Content-Type": "text/plain"}
    )
    assert response.status_code == status.HTTP_415_UNSUPPORTED_MEDIA_TYPE
