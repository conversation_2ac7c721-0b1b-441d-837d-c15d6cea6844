import pytest
from fastapi.testclient import TestClient
from script_generator_server.main import app
from script_generator_server.services.script_generator import ScriptGenerator
from script_generator_server.config import settings


@pytest.fixture
def test_client():
    """Create a test client for the FastAPI app"""
    return TestClient(app)


@pytest.fixture
def mock_script_generator():
    """Create a mock script generator instance"""
    return ScriptGenerator()


@pytest.fixture
def sample_script_request():
    """Sample script generation request data"""
    return {
        "topic": "Benefits of Exercise",
        "tone": "informative",
        "target_audience": "general",
        "duration": 180,
        "language": "en",
        "style": "conversational",
    }


@pytest.fixture
def sample_script_response():
    """Sample script generation response data"""
    return {
        "script_id": "123e4567-e89b-12d3-a456-426614174000",
        "content": "Here's a script about the benefits of exercise...",
        "duration": 180,
        "word_count": 300,
        "sections": [
            {
                "title": "Introduction",
                "content": "Welcome to our video about exercise...",
                "duration": 30,
            }
        ],
    }
