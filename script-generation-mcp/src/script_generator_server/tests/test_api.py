import pytest
from fastapi import status


def test_health_check(test_client):
    """Test the health check endpoint"""
    response = test_client.get("/health")
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {"status": "healthy"}


def test_generate_script_success(test_client, sample_script_request):
    """Test successful script generation"""
    response = test_client.post("/generate", json=sample_script_request)
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert "script_id" in data
    assert "content" in data
    assert "duration" in data
    assert "word_count" in data


def test_generate_script_invalid_request(test_client):
    """Test script generation with invalid request"""
    invalid_request = {"topic": "", "duration": -1}  # Empty topic  # Invalid duration
    response = test_client.post("/generate", json=invalid_request)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


def test_get_script_by_id(test_client):
    """Test retrieving a script by ID"""
    script_id = "test-script-id"
    response = test_client.get(f"/scripts/{script_id}")
    assert response.status_code in [status.HTTP_200_OK, status.HTTP_404_NOT_FOUND]


def test_get_script_invalid_id(test_client):
    """Test retrieving a script with invalid ID"""
    invalid_id = "invalid-id-format"
    response = test_client.get(f"/scripts/{invalid_id}")
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
