import pytest
from script_generator_server.utils.text_processor import (
    calculate_word_count,
    estimate_duration,
    validate_script_request,
)


def test_calculate_word_count():
    """Test word count calculation"""
    text = "This is a sample text with eight words."
    assert calculate_word_count(text) == 8


def test_estimate_duration():
    """Test duration estimation"""
    text = "This is a sample text that should take about 2 seconds to speak."
    duration = estimate_duration(text)
    assert isinstance(duration, int)
    assert duration > 0


def test_validate_script_request_valid():
    """Test script request validation with valid data"""
    valid_request = {
        "topic": "Valid Topic",
        "tone": "professional",
        "duration": 180,
        "language": "en",
    }
    assert validate_script_request(valid_request) is True


def test_validate_script_request_invalid():
    """Test script request validation with invalid data"""
    invalid_request = {
        "topic": "",  # Empty topic
        "tone": "invalid_tone",
        "duration": -1,
    }
    with pytest.raises(ValueError):
        validate_script_request(invalid_request)


@pytest.mark.parametrize("language", ["en", "es", "fr"])
def test_script_generation_different_languages(
    mock_script_generator, sample_script_request
):
    """Test script generation in different languages"""
    sample_script_request["language"] = language
    result = mock_script_generator.generate_script(sample_script_request)
    assert result is not None
    assert "content" in result
