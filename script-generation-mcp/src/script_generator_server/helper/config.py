import os
from dotenv import load_dotenv

from ..loggers.logger import logger

# Load environment variables from a .env file
load_dotenv()


# Access environment variables and store them in variables
DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")

logger.info("config initialized")

HOST = os.getenv("HOST", "localhost")
PORT = int(os.getenv("PORT", 5004))

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")


# Add more variables as needed
# EXAMPLE_VAR = os.getenv('EXAMPLE_VAR')

# You can now use these variables throughout your project
