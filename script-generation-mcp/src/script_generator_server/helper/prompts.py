def build_script_prompt(
    topic: str,
    video_type,
    keywords,
    custom_prompt,
    language: str = "English",
) -> str:
    """
    Builds a detailed prompt for generating video scripts with specific parameters.
    Handles both custom and default prompts with optimization.

    Args:
        topic: Main subject of the video
        custom_prompt: Optional custom prompt to override default
        number_of_scenes: Number of distinct scenes/segments
        tone: Desired tone of the script
        target_duration: Approximate video length
        target_audience: Intended viewer demographic
        language: Script language

    Returns:
        str: Formatted prompt for script generation
    """

    if custom_prompt:
        # Optimize the custom prompt while preserving its intent
        base_prompt = optimize_custom_prompt(custom_prompt)
    else:
        base_prompt = f"""
        Generate a compelling {keywords.get("time")} video script about {topic} for a {keywords.get("audience")} audience in a {keywords.get("tone")} tone.

        Structure Guidelines:
        1. Hook (10-15 seconds):
        - Start with a powerful attention-grabbing statement or question
        - Create immediate emotional connection or curiosity
        - Use concrete examples or surprising statistics when relevant
        - Keep it concise and impactful

        2. Main Content (number_of_scenes based on {video_type}):
        - Break complex information into clear, digestible segments
        - Use smooth transitions between scenes
        - Include specific examples, data, or case studies
        - Address potential questions or counterpoints
        - Maintain a consistent narrative thread
        - Use descriptive language that creates clear mental images

        3. Visual Elements:
        - Describe key visuals needed for each scene
        - Suggest graphics, animations, or B-roll footage
        - Include pauses for important visual elements
        - Consider screen text for key points or data

        4. Engagement Elements:
        - Include rhetorical questions or thought-provoking statements
        - Add moments for viewer reflection
        - Use callbacks to earlier points for coherence
        - Incorporate storytelling elements when appropriate

        5. Pacing Guidelines:
        - Vary sentence length for dynamic delivery
        - Include natural pauses for emphasis
        - Mark points where visuals need time to register
        - Balance information density with clarity

        6. Closing (15-20 seconds):
        - Summarize key takeaways
        - End with a thought-provoking statement or call-to-action
        - Create a sense of completion while maintaining interest
        - Connect back to the opening hook
        """

    # Common parameters and requirements for both custom and default prompts
    technical_requirements = f"""
    Technical Requirements:
    - Write in {language}
    - Avoid technical jargon unless necessary for the topic
    - Use active voice and present tense
    - Format as plain text without markers (no "VOICEOVER:" or similar)
    - Include natural breaks between scenes
    - Target approximately {keywords.get("time")} of speaking time
    """

    content_requirements = """
    Content Requirements:
    - Focus on accuracy and credibility
    - Present information in a logical sequence
    - Balance depth with accessibility
    - Maintain engagement throughout
    - Avoid repetition unless for emphasis
    """

    # Add specific parameters to the prompt
    parameters = f"""
    Additional Parameters:
    Topic: {topic}
    Number of scenes: based on the video type is {video_type}
    Target duration: {keywords.get("time")}
    Tone: {keywords.get("tone")}
    Target audience: {keywords.get("audience")}
    Language: {language}
    Keywords: {keywords}
    """

    # Quality control checklist
    quality_checks = """
    Quality Requirements:
    - Ensure natural, conversational flow
    - Verify timing aligns with target duration
    - Confirm all technical terms are properly explained
    - Check for smooth transitions between scenes
    - Validate that the content matches the intended tone
    """

    # Combine all components
    return f"{base_prompt}\n\n{technical_requirements}\n\n{content_requirements}\n\n{parameters}\n\n{quality_checks}"
