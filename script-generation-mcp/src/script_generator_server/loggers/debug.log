2025-06-14 12:53:26,911 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:26,931 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:26,947 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:26,964 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:26,984 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:26,999 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,015 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,045 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,063 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,081 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,099 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,115 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,130 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,146 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,161 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,176 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,192 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,207 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,223 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,238 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,254 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,270 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,286 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,305 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,321 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,339 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,355 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,371 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,472 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,499 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,515 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,531 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,549 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,565 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,581 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,596 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,612 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,628 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,643 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,658 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,673 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,689 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,704 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,719 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,734 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,750 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,766 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,781 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,797 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,812 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,827 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,844 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,860 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,875 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,891 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,906 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,922 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,937 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,953 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,968 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,983 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:27,999 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,014 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,029 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,044 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,060 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,075 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,090 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,106 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,122 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,136 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,152 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,168 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,184 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,199 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,215 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,231 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,247 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,263 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:28,279 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:50,763 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:50,779 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:50,799 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:50,816 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:50,834 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:50,852 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:50,867 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:50,883 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:50,898 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:50,917 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:50,933 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,077 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,130 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,156 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,184 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,205 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,236 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,259 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,278 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,297 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,319 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,338 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,357 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,377 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,397 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,414 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,453 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,497 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,527 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,546 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,562 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,595 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,619 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,636 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:51,989 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,014 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,035 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,073 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,091 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,108 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,126 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,143 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,160 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,176 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,194 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,212 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,228 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,246 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,271 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,315 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,333 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,351 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,367 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,465 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,481 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,497 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,514 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,532 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,548 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,565 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,582 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,600 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,617 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,634 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,651 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,667 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,684 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,700 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,717 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,737 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,755 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,773 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,790 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,807 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,823 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,839 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,858 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,874 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,891 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 12:53:52,908 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,213 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,231 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,250 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,267 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,286 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,304 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,322 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,340 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,358 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,383 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,401 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,419 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,436 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,454 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,472 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,490 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,508 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,527 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,546 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,564 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,581 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,598 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,616 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,634 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,651 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,669 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,686 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,704 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,727 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,744 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,762 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,781 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,800 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,817 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,835 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,852 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,869 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,887 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,905 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,922 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,940 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,957 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,976 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:37,994 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,011 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,029 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,047 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,065 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,082 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,100 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,117 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,135 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,152 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,170 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,188 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,207 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,226 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,243 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,260 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,278 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,295 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,313 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,331 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,353 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,384 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,472 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,500 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,526 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,551 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,620 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,638 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,656 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,681 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,700 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,718 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,735 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,755 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,774 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,793 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-14 13:09:38,891 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
