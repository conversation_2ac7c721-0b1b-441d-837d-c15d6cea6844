import re
from .workflow import <PERSON><PERSON><PERSON><PERSON>or<PERSON><PERSON><PERSON>

from ..helper.llm_call import generate_ai_script

from ..constants.enum import ScriptType


def fix_invalid_escapes(json_str):
    # Handle specific cases like escaped single quotes
    json_str = re.sub(r"\\\'", "'", json_str)  # Replace \' with just '

    # Then handle other standard JSON escapes
    # JSON standard escape sequences are: \", \\, \/, \b, \f, \n, \r, \t, \uXXXX
    json_str = re.sub(r'\\(?!["\\/bfnrtu]|u[0-9a-fA-F]{4})', r"\\\\", json_str)

    return json_str


async def generate_script(
    topic,
    script_type,
    keywords,
    video_type,
    link=None,
):
    try:

        script_workflow = ScriptWorkFlow()

        script_functions = {
            ScriptType.VIDEO: script_workflow.script_using_video,
            ScriptType.BLOG: script_workflow.script_using_blog,
            ScriptType.TOPIC: script_workflow.script_using_topic,
            ScriptType.SCRIPT: script_workflow.script_using_script,
            ScriptType.AI: generate_ai_script,
        }

        script_function = script_functions.get(script_type)

        if not script_function:
            raise ValueError(f"Invalid script type: {script_type}")

        args = [topic, video_type, keywords]

        if link and (script_type == ScriptType.VIDEO or script_type == ScriptType.BLOG):
            args.insert(3, link)

        script = script_function(*args)

        script_data = {
            "title": topic,
            "script": str(fix_invalid_escapes(str(script))),
            "script_type": script_type,
            "video_type": video_type,
        }

        if link:
            script_data["link"] = link

        return script_data
    except Exception as e:
        print(f"An error occurred in generate_script: {str(e)}")
        raise Exception("Script generation failed")
