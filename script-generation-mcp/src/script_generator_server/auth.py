"""
Simple authentication module for the MCP server.
"""

import secrets
import time
from dataclasses import dataclass
from typing import Dict, List, Optional


@dataclass
class Client:
    client_id: str
    client_secret: str
    scopes: List[str]


@dataclass
class Token:
    access_token: str
    client_id: str
    scopes: List[str]
    expires_at: int  # Unix timestamp


class SimpleAuthProvider:
    """
    A simple authentication provider for demonstration purposes.
    In a real-world scenario, you would integrate with a proper OAuth server.
    """

    def __init__(self):
        self.clients: Dict[str, Client] = {}  # client_id -> Client
        self.tokens: Dict[str, Token] = {}  # access_token -> Token

    def register_client(self, scopes: Optional[List[str]] = None) -> Dict[str, str]:
        """Register a new client."""
        client_id = f"client_{secrets.token_hex(8)}"
        client_secret = secrets.token_hex(16)

        # Use requested scopes or default to all valid scopes
        client_scopes = scopes if scopes else ["script_generate"]

        self.clients[client_id] = Client(
            client_id=client_id,
            client_secret=client_secret,
            scopes=client_scopes,
        )

        return {
            "client_id": client_id,
            "client_secret": client_secret,
            "scopes": client_scopes,
        }

    def create_token(
        self, client_id: str, client_secret: str, scopes: Optional[List[str]] = None
    ) -> Dict[str, str]:
        """Create a new access token."""
        # Validate client credentials
        client = self.clients.get(client_id)
        if not client or client.client_secret != client_secret:
            raise ValueError("Invalid client credentials")

        # Create a new token
        access_token = secrets.token_hex(32)
        expires_in = 3600  # 1 hour

        # Use requested scopes or default to client's scopes
        token_scopes = scopes if scopes else client.scopes

        # Ensure requested scopes are a subset of client's scopes
        if not all(scope in client.scopes for scope in token_scopes):
            raise ValueError("Requested scopes exceed client's authorized scopes")

        self.tokens[access_token] = Token(
            access_token=access_token,
            client_id=client.client_id,
            scopes=token_scopes,
            expires_at=int(time.time()) + expires_in,
        )

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": expires_in,
            "scope": " ".join(token_scopes),
        }

    def validate_token(self, token: str) -> bool:
        """Validate an access token."""
        token_obj = self.tokens.get(token)

        if not token_obj or token_obj.expires_at < int(time.time()):
            return False

        return True

    def revoke_token(self, token: str) -> None:
        """Revoke an access token."""
        if token in self.tokens:
            del self.tokens[token]


# Create a singleton instance
auth_provider = SimpleAuthProvider()
