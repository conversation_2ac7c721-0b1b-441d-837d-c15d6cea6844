import json
from datetime import datetime
from typing import Any, List, Optional, get_args, get_origin

from pydantic import BaseModel, Field, HttpUrl, ValidationInfo, field_validator

from .enum import ScriptType, VideoType


class JSONParsingModel(BaseModel):
    """
    A base Pydantic model that attempts to parse JSON strings for non-primitive fields.
    If a string is provided for a field that expects a complex type (dict, list, or another model),
    it will attempt to parse it as JSON.
    """

    @field_validator("*", mode="before")
    @classmethod
    def _try_parse_json(cls, value: Any, info: ValidationInfo):
        if not isinstance(value, str):
            return value

        field = cls.model_fields.get(info.field_name)
        if not field:
            return value

        field_type = field.annotation
        origin = get_origin(field_type)
        if origin:
            args = get_args(field_type)
            field_type = next(
                (arg for arg in args if arg is not type(None)), field_type
            )

        if field_type in (str, int, float, bool, datetime):
            return value

        try:
            return json.loads(value)
        except json.JSONDecodeError:
            return value


class Keywords(JSONParsingModel):
    time: Optional[str] = None
    objective: Optional[str] = None
    audience: Optional[str] = None
    gender: Optional[str] = None
    tone: Optional[str] = None
    speakers: Optional[List[str]] = None


class GenerateScriptInput(JSONParsingModel):
    topic: str
    script_type: ScriptType = Field(default=ScriptType.TOPIC)
    keywords: Keywords = Field(default_factory=Keywords)
    video_type: VideoType = Field(default=VideoType.SHORT)
    link: Optional[HttpUrl] = None


class GenerateScriptOutput(JSONParsingModel):
    title: str
    script: str
    type: ScriptType
    keywords: Keywords
    video_type: VideoType
    link: Optional[HttpUrl] = None


class ResearchInput(JSONParsingModel):
    topic: str
