"""
Client for testing different connection types to the MCP server.
"""

import asyncio
import argparse
import httpx

from mcp import Client<PERSON>ession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client
from mcp.client.streamable_http import streamablehttp_client
from mcp.types import CallToolResult


async def test_stdio_connection():
    """Test standard I/O connection to the MCP server."""
    print("\n=== Testing Standard I/O Connection ===")

    # Set server parameters
    server_params = StdioServerParameters(
        command="python",  # Path to server.py
        args=["-m", "script_generator_server"],  # Command line arguments
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize session
            await session.initialize()
            print("✅ Connection initialized")

            # Get list of available tools
            tools = await session.list_tools()
            print(f"✅ Available tools: {[tool.name for tool in tools]}")

            # Call the script_generate tool
            print("Calling script_generate tool...")
            result = await session.call_tool(
                "script_generate",
                arguments={
                    "topic": "Test Topic",
                    "script_type": "TOPIC",
                    "keywords": {
                        "time": "2 minutes",
                        "tone": "informative",
                        "audience": "general",
                    },
                    "video_type": "SHORT",
                },
            )

            # Print the result
            print("✅ Result received:")
            print(
                result[0].text[:200] + "..."
                if len(result[0].text) > 200
                else result[0].text
            )


async def test_sse_connection(
    server_url: str = "http://localhost:5001", use_auth: bool = False
):
    """Test SSE connection to the MCP server."""
    print("\n=== Testing SSE Connection ===")

    headers = None
    if use_auth:
        # Register a client and get a token
        token = await get_auth_token(server_url)
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ Authentication token obtained")

    async with sse_client(f"{server_url}/sse", headers=headers) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize session
            await session.initialize()
            print("✅ Connection initialized")

            # Get list of available tools
            tools = await session.list_tools()

            print("tools", tools)

            # Call the script_generate tool
            print("Calling script_generate tool...")
            result = await session.call_tool(
                "script_generate",
                arguments={
                    "topic": "SSE Test Topic",
                    "script_type": "TOPIC",
                    "keywords": {
                        "time": "2 minutes",
                        "tone": "informative",
                        "audience": "general",
                    },
                    "video_type": "SHORT",
                },
            )

            # Print the result
            print("✅ Result received:")
            print(
                result[0].text[:200] + "..."
                if len(result[0].text) > 200
                else result[0].text
            )


async def test_streamable_http_connection(
    server_url: str = "http://localhost:5001", use_auth: bool = False
):
    """Test Streamable HTTP connection to the MCP server."""
    print("\n=== Testing Streamable HTTP Connection ===")

    headers = None
    if use_auth:
        # Register a client and get a token
        token = await get_auth_token(server_url)
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ Authentication token obtained")

    async with streamablehttp_client(f"{server_url}/mcp", headers=headers) as (
        read,
        write,
        _,
    ):
        async with ClientSession(read, write) as session:
            # Initialize session
            await session.initialize()
            print("✅ Connection initialized")

            # Get list of available tools
            tools = await session.list_tools()
            print("tools", tools)
            # print(f"✅ Available tools: {[tool.name for tool in tools]}")

            # Call the script_generate tool
            print("Calling script_generate tool...")
            result = await session.call_tool(
                "script_generate",
                arguments={
                    "topic": "Streamable HTTP Test Topic",
                    "script_type": "TOPIC",
                    "keywords": {
                        "time": "2 minutes",
                        "tone": "informative",
                        "audience": "general",
                    },
                    "video_type": "SHORT",
                },
            )

            # Print the result
            print("✅ Result received:", result.content)
            print(
                result.content[0].text[:200] + "..."
                if len(result.content[0].text) > 200
                else result.content[0].text
            )


async def get_auth_token(server_url: str) -> str:
    """Register a client and get an authentication token."""
    async with httpx.AsyncClient() as client:
        # Register a client
        response = await client.post(
            f"{server_url}/oauth/register",
            json={"scopes": ["script_generate"]},
        )
        client_data = response.json()

        # Get a token
        response = await client.post(
            f"{server_url}/oauth/token",
            json={
                "client_id": client_data["client_id"],
                "client_secret": client_data["client_secret"],
                "scopes": ["script_generate"],
            },
        )
        token_data = response.json()

        return token_data["access_token"]


async def main():
    """Main function to run the client."""
    parser = argparse.ArgumentParser(description="Test MCP server connections")
    parser.add_argument(
        "--connection-type",
        choices=["stdio", "sse", "streamable-http", "all"],
        default="all",
        help="Type of connection to test",
    )
    parser.add_argument(
        "--server-url",
        default="http://localhost:5001",
        help="URL of the server for SSE and Streamable HTTP connections",
    )
    parser.add_argument(
        "--auth",
        action="store_true",
        help="Use authentication for SSE and Streamable HTTP connections",
    )

    args = parser.parse_args()

    if args.connection_type == "stdio" or args.connection_type == "all":
        await test_stdio_connection()

    if args.connection_type == "sse" or args.connection_type == "all":
        await test_sse_connection(args.server_url, args.auth)

    if args.connection_type == "streamable-http" or args.connection_type == "all":
        await test_streamable_http_connection(args.server_url, args.auth)


if __name__ == "__main__":
    asyncio.run(main())
