import asyncio
from mcp import Client<PERSON>ession, StdioServerParameters
from mcp.client.stdio import stdio_client


async def main():
    # Set server parameters
    server_params = StdioServerParameters(
        command="content-extractor-mcp",  # Path to server.py
        args=[
            "C://Users//INDIA//Desktop//mcp//content-extractor-mcp-MCP-server//content_extractor_mcp//src//content_extractor_mcp//server.py",
            "content-extractor-mcp",
            "run",
            "content-extractor-mcp",
        ],  # Command line arguments (if needed)
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize session
            await session.initialize()

            try:
                # Get list of available tools
                tools = await session.list_tools()

                print(f"Available tools ================: {tools}")

                # Example of calling a tool
                tool_result = await session.call_tool(
                    "fire_crawl",
                    arguments={
                        # "script": """i want article. My topic is AI and keyword is CV and video type is short. Please used only script_using_topic mcp server and give me only output of server.""",
                        "link": "https://blogs.nvidia.com/blog/rtx-ai-garage-nim-blueprints-g-assist/",
                    },
                )

                print(
                    f"Tool execution result =============================: {tool_result}"
                )

            except Exception as e:
                print(f"An error occurred: {e}")


if __name__ == "__main__":
    print("Running")
    asyncio.run(main())
