import os
from dotenv import load_dotenv
from content_extractor_mcp.helper.utils import create_dir

# Load environment variables from a .env file
load_dotenv()


create_dir("./temp")

# Access environment variables and store them in variables
DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")

HOST = os.getenv("HOST", "localhost")
PORT = int(os.getenv("PORT", 5004))

ASSEMBLY_AI_API_KEY = os.getenv("ASSEMBLY_AI_API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
FIRECRAWL_API_KEY = os.getenv("FIRECRAWL_API_KEY")

# Add more variables as needed
# EXAMPLE_VAR = os.getenv('EXAMPLE_VAR')

# You can now use these variables throughout your project
