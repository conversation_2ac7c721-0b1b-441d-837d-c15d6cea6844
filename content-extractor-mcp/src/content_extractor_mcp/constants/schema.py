from pydantic import BaseModel, HttpUrl, constr, Field
from typing import List


class GenerateSubtitle(BaseModel):
    audio_urls: List[HttpUrl] = Field(..., description="List of audio URLs is required")
    script: constr(min_length=1, max_length=1000) = Field(
        ..., description="Script is required"
    )


class ScrapeWeb(BaseModel):
    link: HttpUrl = Field(..., description="Link is required")


class FireCrawlScrapeWeb(BaseModel):
    link: HttpUrl = Field(..., description="Link is required")
    raw_data: bool = Field(..., description="yes or no")
