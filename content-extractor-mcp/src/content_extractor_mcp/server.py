from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
from pydantic import AnyUrl
import mcp.server.stdio
import json
import uvicorn
import logging
import contextlib

from starlette.applications import Starlette
from starlette.routing import Route

from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware

from mcp.server.sse import SseServerTransport
from mcp.server.streamable_http_manager import StreamableHTTPSessionManager

from content_extractor_mcp.constants.enum import Tools
from content_extractor_mcp.constants.constant import Descriptions
from content_extractor_mcp.constants.schema import (
    GenerateSubtitle,
    ScrapeWeb,
    FireCrawlScrapeWeb,
)

from content_extractor_mcp.helper.config import HOST, PORT
from content_extractor_mcp.services.subtitle import generate_subtitle
from content_extractor_mcp.services.web_scrap import extract_article  # , scrape_website
from .services.browser_tool import ak
from .services.filecrawl_tool import WebCrawler


# from .services.filecrawl_tool import WebCrawler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Store notes as a simple key-value dict to demonstrate state management
notes: dict[str, str] = {}

server = Server("content-extractor-mcp")


@server.list_resources()
async def handle_list_resources() -> list[types.Resource]:
    """
    List available note resources.
    Each note is exposed as a resource with a custom note:// URI scheme.
    """
    return [
        types.Resource(
            uri=AnyUrl(f"note://internal/{name}"),
            name=f"Note: {name}",
            description=f"A simple note named {name}",
            mimeType="text/plain",
        )
        for name in notes
    ]


@server.read_resource()
async def handle_read_resource(uri: AnyUrl) -> str:
    """
    Read a specific note's content by its URI.
    The note name is extracted from the URI host component.
    """
    if uri.scheme != "note":
        raise ValueError(f"Unsupported URI scheme: {uri.scheme}")

    name = uri.path
    if name is not None:
        name = name.lstrip("/")
        return notes[name]
    raise ValueError(f"Note not found: {name}")


@server.list_prompts()
async def handle_list_prompts() -> list[types.Prompt]:
    """
    List available prompts.
    Each prompt can have optional arguments to customize its behavior.
    """
    return [
        types.Prompt(
            name="summarize-notes",
            description="Creates a summary of all notes",
            arguments=[
                types.PromptArgument(
                    name="style",
                    description="Style of the summary (brief/detailed)",
                    required=False,
                )
            ],
        )
    ]


@server.get_prompt()
async def handle_get_prompt(
    name: str, arguments: dict[str, str] | None
) -> types.GetPromptResult:
    """
    Generate a prompt by combining arguments with server state.
    The prompt includes all current notes and can be customized via arguments.
    """
    if name != "summarize-notes":
        raise ValueError(f"Unknown prompt: {name}")

    style = (arguments or {}).get("style", "brief")
    detail_prompt = " Give extensive details." if style == "detailed" else ""

    return types.GetPromptResult(
        description="Summarize the current notes",
        messages=[
            types.PromptMessage(
                role="user",
                content=types.TextContent(
                    type="text",
                    text=f"Here are the current notes to summarize:{detail_prompt}\n\n"
                    + "\n".join(
                        f"- {name}: {content}" for name, content in notes.items()
                    ),
                ),
            )
        ],
    )


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """
    List available tools.
    Each tool specifies its arguments using JSON Schema validation.
    """
    return [
        types.Tool(
            name=Tools.GENERATE_SUBTITLE,
            description=Descriptions.GENERATE_SUBTITLE,
            inputSchema=GenerateSubtitle.schema(),
        ),
        types.Tool(
            name=Tools.SCRAPE_WEB,
            description=Descriptions.SCRAPE_WEB,
            inputSchema=ScrapeWeb.schema(),
        ),
        types.Tool(
            name=Tools.SCRAPE_WEB_USING_BROWSER_USE,
            description=Descriptions.SCRAPE_WEB_USING_BROWSER_USE,
            inputSchema=ScrapeWeb.schema(),
        ),
        types.Tool(
            name=Tools.SCRAPE_WEB_USING_FIRE_CRAWLER,
            description=Descriptions.SCRAPE_WEB_USING_FIRE_CRAWL,
            inputSchema=FireCrawlScrapeWeb.schema(),
        ),
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict | None) -> types.CallToolResult:
    """
    Handle tool execution requests.
    Tools can modify server state and notify clients of changes.
    """

    try:

        match name:
            case Tools.GENERATE_SUBTITLE:

                audio_urls = arguments.get("audio_urls")
                script = arguments.get("script")

                subtitles = await generate_subtitle(
                    script,
                    audio_urls,
                )

                # subtitles = """1\n00:00:00,160 --> 00:00:04,024\nNew Nvidia tech drops Are you ready to upgrade? Hey there. Got a\n\n2\n00:00:04,032 --> 00:00:06,616\nminute to catch up on Nvidia's latest buzz?\n\n3\n00:00:06,728 --> 00:00:10,456\nThey're making waves yet again this October with some\n\n4\n00:00:10,528 --> 00:00:13,816\nepic announcements. From breaking the mold in AI\n\n5\n00:00:13,848 --> 00:00:17,112\nrobotics to exploring new quantum realms, NIDIA is all\n\n6\n00:00:17,136 --> 00:00:20,488\nabout stretching the tech boundaries. But what's in it for you?\n\n7\n00:00:20,624 --> 00:00:24,344\nFirst on the list, Nvidia Research showcased this wild robotic\n\n8\n00:00:24,392 --> 00:00:27,586\nhack doing intricate P10 tricks. Who knew\n\n9\n00:00:27,618 --> 00:00:31,346\nAI could make robots so skillful, right? It's a real game changer\n\n10\n00:00:31,378 --> 00:00:34,802\nfor industries diving deep into automation. And just\n\n11\n00:00:34,826 --> 00:00:38,114\nwhen you thought that was it, boom. NVTIA hosted\n\n12\n00:00:38,162 --> 00:00:42,098\na killer developer day sessions were buzzing about each aspect\n\n13\n00:00:42,194 --> 00:00:45,618\nof AI and quantum computing. Paul Graham talked about\n\n14\n00:00:45,674 --> 00:00:49,714\nsqueezing power out of GPUs like a pro, something our data crunching\n\n15\n00:00:49,762 --> 00:00:53,954\nfuncture relies on heavily. From battling climate change to innovating\n\n16\n00:00:54,002 --> 00:00:57,474\nwith conversational AI, Nvidia's tech tackles real problems\n\n17\n00:00:57,522 --> 00:01:01,298\nhead on, like creating AI that talks, learns and even\n\n18\n00:01:01,354 --> 00:01:04,514\npredicts. Pretty rag if you ask me.\n\n19\n00:01:04,682 --> 00:01:07,794\nPersonally, I think Nvidia is not just on the cutting edge,\n\n20\n00:01:07,922 --> 00:01:11,826\nthey're basically sketching the future of digital life. Imagine the impact\n\n21\n00:01:11,898 --> 00:01:15,906\nof AI driven cybersecurity or generative AI making music.\n\n22\n00:01:15,978 --> 00:01:19,408\nIsn't that something to think about? So are you as pumped about\n\n23\n00:01:19,464 --> 00:01:23,152\nthese tech marvels? Hit subscribe for more tech updates or drop your\n\n24\n00:01:23,176 --> 00:01:27,152\nthoughts below. Which Nvidia breakthrough has got you? Ped Let the\n\n25\n00:01:27,176 --> 00:01:27,840\ntech talk begin.\n\n"""

                result = {"subtitle": subtitles}

            case Tools.SCRAPE_WEB:

                link = arguments.get("link")

                if not link:
                    raise ValueError("Missing name or content")

                content = await extract_article(link)

                result = {"content": content}

            case Tools.SCRAPE_WEB_USING_BROWSER_USE:

                link = arguments.get("link")

                if not link:
                    raise ValueError("Missing name or content")

                content = await ak(link)

                result = {"content": content}

            case Tools.SCRAPE_WEB_USING_FIRE_CRAWLER:

                link = arguments.get("link")
                raw_data = arguments.get("raw_data")

                if not link:
                    raise ValueError("Missing name or content")

                content = await WebCrawler().scrape(link)

                result = {"content": content}

            case _:
                return [types.TextContent(type="text", text=f"Unknown tool: {name}")]

    except Exception as error:
        print("Error: ", error)
        error = {"message": f"Error: {str(error)}", "is_error": True}
        return [types.TextContent(type="text", text=json.dumps(error, indent=2))]

    print("result", result)

    return [types.TextContent(type="text", text=json.dumps(result, indent=2))]


async def main():
    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="content-extractor-mcp",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )


def create_app():

    sse = SseServerTransport("/content")

    # Create the session manager with the event store
    try:
        # Try with auth parameters (newer MCP versions)
        session_manager = StreamableHTTPSessionManager(
            app=server,
            # event_store=event_store,  # Use our event store for resumability
            json_response=False,  # Use SSE format for responses
            stateless=False,  # Stateful mode for better user experience
            # auth_server_provider=auth_provider,
            # auth_settings=auth_settings,
        )
        logger.info(
            "StreamableHTTPSessionManager initialized with authentication support"
        )
    except TypeError:
        # Fallback for older MCP versions that don't support auth
        logger.warning(
            "Your MCP version doesn't support authentication in StreamableHTTPSessionManager"
        )
        logger.warning(
            "Initializing StreamableHTTPSessionManager without authentication"
        )

        # Try with just the basic parameters
        try:
            session_manager = StreamableHTTPSessionManager(
                app=server,
                # event_store=event_store,
                json_response=False,
            )
            logger.info(
                "StreamableHTTPSessionManager initialized without authentication"
            )
        except TypeError:
            # If that still fails, try with minimal parameters
            logger.warning(
                "Falling back to minimal StreamableHTTPSessionManager initialization"
            )
            session_manager = StreamableHTTPSessionManager(app=server)
    except Exception as e:
        logger.error(f"Failed to initialize StreamableHTTPSessionManager: {e}")
        session_manager = None

    class HandleSSE:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            async with self.sse.connect_sse(scope, receive, send) as streams:
                await server.run(
                    streams[0],
                    streams[1],
                    server.create_initialization_options(),
                )

    class HandleMessages:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            await self.sse.handle_post_message(scope, receive, send)

    class HandleStreamableHttp:
        def __init__(self, session_manager):
            self.session_manager = session_manager

        async def __call__(self, scope, receive, send):
            if self.session_manager is not None:
                try:
                    logger.info("Handling Streamable HTTP connection ....")
                    await self.session_manager.handle_request(scope, receive, send)
                    logger.info("Streamable HTTP connection closed ....")
                except Exception as e:
                    logger.error(f"Error handling Streamable HTTP request: {e}")
            else:
                # Return a 501 Not Implemented response if streamable HTTP is not available
                await send(
                    {
                        "type": "http.response.start",
                        "status": 501,
                        "headers": [(b"content-type", b"application/json")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": json.dumps(
                            {"error": "Streamable HTTP transport is not available"}
                        ).encode("utf-8"),
                    }
                )

    routes = [
        Route("/sse", endpoint=HandleSSE(sse), methods=["GET"]),
        Route("/content", endpoint=HandleMessages(sse), methods=["POST"]),
    ]

    # Add Streamable HTTP route if available
    if session_manager is not None:
        routes.append(
            Route(
                "/mcp", endpoint=HandleStreamableHttp(session_manager), methods=["POST"]
            )
        )

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
        )
    ]

    # Define lifespan for session manager
    @contextlib.asynccontextmanager
    async def lifespan(app):
        """Context manager for session manager."""
        if session_manager is not None:
            async with session_manager.run():
                logger.info("Application started with StreamableHTTP session manager!")
                try:
                    yield
                finally:
                    logger.info("Application shutting down...")
        else:
            # No session manager, just yield
            yield

    return Starlette(routes=routes, middleware=middleware, lifespan=lifespan)


def start_server():
    app = create_app()
    logger.info(f"Starting server at {HOST}:{PORT}")
    uvicorn.run(app, host=HOST, port=PORT, timeout_keep_alive=120)


if __name__ == "__main__":
    while True:
        try:
            start_server()
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
            break
        except Exception as e:
            logger.error(f"Server crashed with error: {e}")
            continue
