import requests
from bs4 import Beautiful<PERSON>oup
from fake_useragent import UserAgent
import os
import urllib.parse
# from .proxy import proxy_list
import re
import time
import random
from typing import Annotated
from .utils import is_valid_url


# paragraph gen
def fetch_paragraphs_from_url(url: str) -> str:
    """
    Fetches paragraphs from a single URL.
    Parameters:
    url (str): The URL of the webpage to fetch paragraphs from.
    Returns:
    str: A string containing at most three non-blank, non-ad paragraphs found on the webpage.
    """
    all_paragraphs = ""
    # Send a GET request to the URL
    response = requests.get(url, headers={"User-Agent": "Mozilla/5.0"})
    # Check if the request was successful (status code 200)
    if response.status_code == 200:
        # Parse the HTML content of the page
        soup = BeautifulSoup(response.content, "html.parser")
        # Find all paragraphs in the HTML
        paragraphs = soup.find_all("p")
        # Define a list of keywords that are commonly found in ads
        ad_keywords = [
            "advertisement",
            "ads",
            "sponsored",
            "promo",
            "promotion",
            "click here",
            "buy now",
        ]
        # Extract text from each paragraph and concatenate it to the result string
        paragraph_count = 0
        for paragraph in paragraphs:
            paragraph_text = (
                paragraph.get_text().strip()
            )  # Remove leading/trailing whitespace
            # Check if paragraph is non-empty and does not contain ad-related keywords
            if paragraph_text and not any(
                ad_keyword in paragraph_text.lower() for ad_keyword in ad_keywords
            ):
                all_paragraphs += paragraph_text + "\n"
                paragraph_count += 1
                if paragraph_count == 10:  # Maximum of ten non-blank paragraphs
                    break
    return all_paragraphs


async def extract_article(url):
    # proxy_lists = proxy_list()
    headers = {
        "User-Agent": UserAgent().random,
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Referer": "https://www.google.com/",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
    }
    session = requests.Session()
    for _ in range(3):  # Try up to 3 times
        proxy = "http://pbldvjdg-rotate:<EMAIL>:80/"
        proxies = {
            "https": proxy,
            "http": proxy,
        }
        try:
            response = session.get(url, headers=headers, proxies=proxies, timeout=10)
            response.raise_for_status()  # Raise an exception for bad status codes
            # Parse the HTML content with BeautifulSoup
            soup = BeautifulSoup(response.content, "html.parser")
            # Extract the article content
            article_content = soup.find("article")
            if article_content:
                content = article_content.get_text()
            else:
                content = soup.get_text()
            return content
        except requests.RequestException as e:
            print(f"Request failed: {e}")
            time.sleep(random.uniform(1, 3))  # Wait before retrying
    raise Exception("Failed to extract article after multiple attempts")


def scrape_website(
    urls: Annotated[list, "The list of urls of the website"]
) -> Annotated[str, "The search results"]:
    """
    Scrape a website and fetch video or image URLs

    Args:
        url (str): The URL of the website to scrape

    Returns:
        list: A list of URLs of the scraped media
    """

    # Extract the media URLs from the elements
    media_urls = []

    for url in urls:
        try:
            # Send a request to the website and get the HTML response
            response = requests.get(url)
            html = response.content

            # Parse the HTML content using BeautifulSoup
            soup = BeautifulSoup(html, "html.parser")

            # Find all media elements on the page
            image_media_elements = []
            video_media_elements = []

            video_media_elements = soup.find_all("video")

            image_media_elements = soup.find_all("img")

            media_elements = soup.find_all()

            for element in video_media_elements:
                src = element.get("src")
                if src:
                    media_urls.append(src)

            for element in image_media_elements:
                src = element.get("src")
                if src:
                    media_urls.append(src)

            for element in media_elements:
                # Check if the element is a link to a video or image
                if element.name == "a" and element.get("href"):
                    # Check if the link is to a video or image
                    if element.get("href").endswith(
                        (".mp4", ".webm", ".ogg", ".jpg", ".jpeg", ".png", ".gif")
                    ):
                        media_urls.append(element.get("href"))

            # Find all script tags that contain media URLs
            script_tags = soup.find_all("script")

            for script in script_tags:
                script_text = script.text
                urls = re.findall(r'src=[\'"]?([^\'" >]+)', script_text)
                media_urls.extend(urls)

        except Exception as err:
            print(f"[-] Error: {str(err)}")

    print(media_urls)

    # Remove duplicates and filter out invalid URLs
    media_urls = list(set(filter(is_valid_url, media_urls)))

    return media_urls


def scrape_article(url):

    # proxy_lists = proxy_list()
    headers = {
        "User-Agent": UserAgent().random,
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Referer": "https://www.google.com/",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
    }

    session = requests.Session()

    for _ in range(3):  # Try up to 3 times
        proxy = "http://pbldvjdg-rotate:<EMAIL>:80/"
        proxies = {
            "https": proxy,
            "http": proxy,
        }

        try:
            response = session.get(url, headers=headers, proxies=proxies, timeout=10)
            response.raise_for_status()  # Raise an exception for bad status codes

        except requests.RequestException as e:
            print(f"Request failed: {e}")
            time.sleep(random.uniform(1, 3))  # Wait before retrying

    # Parse the HTML content
    soup = BeautifulSoup(response.content, "html.parser")

    # Extract the article title
    title = soup.find("h1").text.strip() if soup.find("h1") else "No title found"

    # Extract the main text content
    paragraphs = soup.find_all("p")
    text_content = "\n\n".join([p.text for p in paragraphs])

    # Extract images
    images = []
    for img in soup.find_all("img"):
        src = img.get("src")
        if src:
            images.append(urllib.parse.urljoin(url, src))

    # Extract videos
    videos = []
    for video in soup.find_all("video"):
        src = video.get("src")
        if src:
            videos.append(urllib.parse.urljoin(url, src))

    # If no video tag, look for iframe (e.g., YouTube embeds)
    if not videos:
        for iframe in soup.find_all("iframe"):
            src = iframe.get("src")
            if src and ("youtube.com" in src or "vimeo.com" in src):
                videos.append(src)

    return {"title": title, "text": text_content, "images": images, "videos": videos}


def save_content(content, output_dir):
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Save title and text content
    with open(os.path.join(output_dir, "article.txt"), "w", encoding="utf-8") as f:
        f.write(f"Title: {content['title']}\n\n")
        f.write(content["text"])

    # Save image URLs
    with open(os.path.join(output_dir, "images.txt"), "w") as f:
        for img_url in content["images"]:
            f.write(f"{img_url}\n")

    # Save video URLs
    with open(os.path.join(output_dir, "videos.txt"), "w") as f:
        for video_url in content["videos"]:
            f.write(f"{video_url}\n")
