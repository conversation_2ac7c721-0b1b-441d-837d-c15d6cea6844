from content_extractor_mcp.helper.utils import is_valid_url, create_dir, clean_dir
from content_extractor_mcp.providers.assembly_ai import generate_subtitles
import os
import uuid
import aiohttp
import asyncio
from moviepy import AudioFileClip, concatenate_audioclips


async def download_audio_file(session, video_id, audio_url, file_path):
    if is_valid_url(audio_url):
        async with session.get(audio_url) as response:
            if response.status == 200:
                with open(file_path, "wb") as f:
                    f.write(await response.read())
        print(f"Downloaded audio to {file_path}")
    else:
        # If it's a local file, just return the path
        return f"./temp/{video_id}/{audio_url}"
    return file_path


async def generate_subtitle(script, audio_links):
    try:
        video_id = uuid.uuid4()
        create_dir(f"./temp/{video_id}")
        tts_path = f"./temp/{video_id}/audio.mp3"

        # Create list of file paths for downloaded audio
        file_paths = []

        # Download all audio files in parallel
        async with aiohttp.ClientSession() as session:
            download_tasks = []
            for index, audio_url in enumerate(audio_links):
                file_path = os.path.join(
                    os.getcwd(),
                    f"./temp/{video_id}/temp_audio{index}.mp3",
                )
                task = download_audio_file(session, video_id, audio_url, file_path)
                download_tasks.append(task)

            # Wait for all downloads to complete
            file_paths = await asyncio.gather(*download_tasks)

        # Process audio files
        audio_clips = [
            AudioFileClip(path) for path in file_paths if os.path.exists(path)
        ]

        audio_clip = concatenate_audioclips(audio_clips)

        if not os.path.exists(tts_path):
            # Write the combined audio to the output file
            audio_clip.write_audiofile(tts_path)

        voice = "en_us_001"
        voice_prefix = voice[:2]

        # Split script into sentences
        sentences = script.split(". ")
        sentences = list(filter(lambda x: x != "", sentences))

        subtitles = generate_subtitles(
            audio_path=tts_path,
            sentences=sentences,
            audio_clips=[audio_clip],
            voice=voice_prefix,
        )

        clean_dir(f"./temp/{video_id}")

        return subtitles
    except Exception as err:
        print(f"[-] Error generate_subtitles: {str(err)}")
        clean_dir(f"./temp/{video_id}")  # Ensure cleanup on error
        raise err
