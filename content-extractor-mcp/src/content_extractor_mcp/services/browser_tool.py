import asyncio
from .browseruse_tool import (
    main as extract_content,
)  # new_1 import main as extract_content


# async def async_main(url):
#     """Async wrapper for main script functionality."""
#     # Get URL from user
#     target_url = url
#     # Extract content
#     try:
#         result = await extract_content(target_url)
#         print("\n--- Extraction Complete ---")
#         print("Results have been saved to 'extraction_output.txt'")
#         return result
#     except Exception as e:
#         print(f"An error occurred during extraction: {e}")


# def ak(url):
#     """Main entry point for the script."""
#     loop = asyncio.get_event_loop()
#     if loop.is_running():
#         # return url
#         # If an event loop is already running, schedule the coroutine
#         task = loop.run_until_complete(async_main(url))
#         return task
#         # return "File creation task started."
#     else:
#         asyncio.run(async_main(url))  # Normal execution if no loop is running
#         return "File created."


async def async_main(url):
    """Async wrapper for main script functionality."""
    # Get URL from user
    target_url = url

    # Extract content
    try:
        result = await extract_content(target_url)
        print("\n--- Extraction Complete ---")
        print("Results have been saved to 'extraction_output.txt'")
        return result
    except Exception as e:
        print(f"An error occurred during extraction: {e}")


def ak(url):
    """
    Main entry point for the script.
    Uses async_main with an existing event loop if present.
    """
    try:
        # Check if an event loop is already running
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If loop is running, use create_task
            return loop.create_task(async_main(url))
            # return "file created"
        else:
            # If no loop is running, use run_until_complete
            return loop.run_until_complete(async_main(url))
            # return "file created"
    except RuntimeError:
        # If no event loop exists, create a new one
        return asyncio.run(async_main(url))
