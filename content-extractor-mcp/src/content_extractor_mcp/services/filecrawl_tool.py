from firecrawl import FirecrawlApp
from ..helper.config import FIRECRAWL_API_KEY


class WebCrawler:
    def __init__(self):
        self.app = FirecrawlApp(api_key=FIRECRAWL_API_KEY)

    async def scrape(self, link):
        response = self.app.scrape_url(
            url=link,
            params={
                "formats": ["markdown"],
            },
        )

        # Extract the markdown content from the response dictionary
        if isinstance(response, dict) and "markdown" in response:
            return response["markdown"]
        elif isinstance(response, dict):
            # Return a stringified version of the response if markdown key isn't present
            return str(response)
        else:
            # If response is already a string or other type
            return str(response)
