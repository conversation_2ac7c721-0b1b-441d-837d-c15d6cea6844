import asyncio
import re
from typing import Optional, List
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeA<PERSON>
from browser_use.agent.views import AgentStepInfo
from browser_use import Agent

# Configuration
GEMINI_API_KEY = (
    "AIzaSyBtO7sB7eZXiefdPrUNFI6Nd_BloPCgx7A"  # Replace with your actual API key
)
MODEL_CONFIG = "gemini-1.5-flash"
TEMPERATURE_CONFIG = 0.0
MAX_TOKENS_CONFIG = 131072

# Main Gemini LLM
llm = ChatGoogleGenerativeAI(
    model=MODEL_CONFIG,
    google_api_key=GEMINI_API_KEY,
    temperature=TEMPERATURE_CONFIG,
    max_output_tokens=MAX_TOKENS_CONFIG,
)

# Thinking LLM
llm_think = ChatGoogleGenerativeAI(
    model=MODEL_CONFIG,
    google_api_key=GEMINI_API_KEY,
    temperature=TEMPERATURE_CONFIG,
    max_output_tokens=MAX_TOKENS_CONFIG,
)


class ExtractingAgent(Agent):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._extracted_values: List[str] = []
        self.all_extractions: List[str] = []

    async def step(self, step_info: Optional[AgentStepInfo] = None) -> None:
        await super().step(step_info)
        if self.state.last_result:
            for result in self.state.last_result:
                if result.extracted_content:
                    self._extracted_values.append(result.extracted_content)

    def get_extracted_values(self) -> List[str]:
        return self._extracted_values

    def collect_all_extractions(self):
        for step_history in self.state.history.history:
            if step_history.result:
                for action_result in step_history.result:
                    if action_result.extracted_content:
                        self.all_extractions.append(action_result.extracted_content)
        return self.all_extractions


async def process_extractions_with_gemini(extractions: List[str], api_key: str) -> str:
    # Remove excessive whitespace from extractions
    def clean_text(text):
        return re.sub(r"\n{3,}", "\n\n", text.strip())

    # Clean and combine extractions
    cleaned_extractions = [clean_text(ext) for ext in extractions]
    combined_extractions = "\n\n".join(cleaned_extractions)

    # Use Gemini to structure the data
    gemini_model = ChatGoogleGenerativeAI(
        model="gemini-1.5-flash",
        google_api_key=api_key,
        temperature=0.0,
        max_output_tokens=131072,
    )

    # Prompt for structuring the data
    prompt_text = f"""
    Carefully review the following extracted web content:

    {combined_extractions}

    Please:
    1. Convert this raw, unstructured data into a well-organized markdown format.
    2. Extract text information, create meaningful summary of the content.
    3. Summary have meaningful insights and key points.
    4. Extract all image links and create new section as 'image links'
    5. Extract all video links and create new section as 'video links'
    6. When created file please or must be followed this file structure.
       1. Text Summary
       2. Image Links
       3. Video Links

    Output format should be comprehensive markdown with proper headings, bullet points, and clear structure.
    """

    try:
        # Generate structured response using messages
        response = await gemini_model.ainvoke(prompt_text)

        # Extract the text from the response
        if response:
            # Clean the output to remove excessive whitespace
            cleaned_output = re.sub(r"\n{3,}", "\n\n", response.content.strip())
            return cleaned_output
        else:
            return "Unable to process extractions: Empty response content."

    except Exception as e:
        error_message = f"Error processing extractions with Gemini API: {e}"
        print(error_message)
        return error_message


async def extract_url_content(target_url: str) -> str:
    # Create ExtractingAgent
    agent = ExtractingAgent(
        task=f"""
        Extract comprehensive information from {target_url}:
        - Scrape all content thoroughly
        - Check internal links for additional context
        - Perform in-depth research
        - Prepare a detailed, structured report
        - max step consider is 3 
        """,
        llm=llm,
        planner_llm=llm_think,
        page_extraction_llm=llm,
    )

    # Run the agent
    await agent.run()

    # Collect all extractions
    all_extractions = agent.collect_all_extractions()

    # Clean extractions
    def clean_extraction(text):
        return re.sub(r"\n{3,}", "\n\n", text.strip())

    cleaned_extractions = [clean_extraction(ext) for ext in all_extractions]

    # Write all extractions to a file
    with open("all_extractions.txt", "w", encoding="utf-8") as f:
        f.write("\n\n".join(cleaned_extractions))

    # Process extractions with Gemini
    processed_output = await process_extractions_with_gemini(
        all_extractions, GEMINI_API_KEY
    )

    # Save processed output
    with open("extraction_output.txt", "w", encoding="utf-8") as f:
        f.write(processed_output)

    return processed_output


# Main execution function
async def main(target_url: str):
    result = await extract_url_content(target_url)
    print("Processed Extraction Output:")
    print(result)
    return result
