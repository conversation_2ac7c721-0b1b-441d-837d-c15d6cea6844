from content_extractor_mcp.services.subtitle import generate_subtitle


async def test_subtitle():
    res = await generate_subtitle(
        script="Missed Nvidia's GTC 2025? Here's your quick recap! In just a minute, let's dive into what Nvidia's latest event revealed about the future of technology.You won't believe what went down at the GPU Technology Conference 2025. Nvidia's making waves with some cutting-edge advancements in AI, robotics, and high-performance computing. These breakthroughs are pushing us right into the tech of tomorrow.First—the GROOT AI model is your new robotic bestie, equipped with lightning-fast reflexes and decision-making that matches human capabilities. And the Blackwell Ultra GPU series is grabbing headlines as the fastest AI hardware yet, bringing significant power to AI and computational tasks. Let's not forget about their Spectrum-X Ethernet Switch delivering speeds of 1.6 terabits per second per port.With these moves, Nvidia's reshaping tech ecosystems, drawing out a future where everything's more efficient, on a massive scale, with limitless innovation.Want to know more about what's coming next? Hit that like button if you're as excited as I am, and make sure to subscribe for the latest in tech.So there you have it—how Nvidia's lighting the way for a smarter future. Don’t miss out, like and subscribe for more insider info!.",
        audio_links=[
            "https://peregrine-results.s3.amazonaws.com/ah04jVk5xWjQ7Alof0.mp3",
            "https://peregrine-results.s3.amazonaws.com/YdKFwdw6PmoIrQnRzz.mp3",
            "https://peregrine-results.s3.amazonaws.com/M2886dfyeJFCIs0uZS.mp3",
            "https://peregrine-results.s3.amazonaws.com/tsbdhOzUdtrUsWTxJc.mp3",
            "https://peregrine-results.s3.amazonaws.com/sLOtZFTFgsfTY8hQIy.mp3",
            "https://peregrine-results.s3.amazonaws.com/2MjX8BuZghEqgQVBmK.mp3",
            "https://peregrine-results.s3.amazonaws.com/gtmOMAfpNYR2Owj5d6.mp3",
            "https://peregrine-results.s3.amazonaws.com/mmfTszfwni17dUq6iq.mp3",
        ],
    )
    print(res)


if __name__ == "__main__":
    import asyncio

    asyncio.run(test_subtitle())
