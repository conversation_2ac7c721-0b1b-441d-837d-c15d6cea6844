[project]
name = "content-extractor-mcp"
version = "0.1.0"
description = "This tool use to extract content from the web using article, t"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.11.18",
    "assemblyai>=0.37.0",
    "autogen-agentchat>=0.2.40",
    "autogen-core>=0.0.2",
    "autogen-ext[openai]>=0.0.1",
    "beautifulsoup4>=4.13.3",
    "browser-use>=0.1.40",
    "chromium>=0.0.0",
    "fake-useragent>=2.1.0",
    "firecrawl>=1.16.0",
    "langchain-google-genai>=2.1.1",
    "mcp==1.9.0",
    "moviepy>=2.1.2",
    "playwright>=1.51.0",
    "requests>=2.32.3",
    "srt-equalizer>=0.1.10",
]
[[project.authors]]
name = "<PERSON><PERSON><PERSON>"
email = "<EMAIL>"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
content-extractor-mcp = "content_extractor_mcp:main"
