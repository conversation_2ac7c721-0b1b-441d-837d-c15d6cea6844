# content-extractor-mcp MCP server

This tool is used to extract content from the web using articles.

## Components

### Resources

The server implements a simple note storage system with:

- Custom note:// URI scheme for accessing individual notes
- Each note resource has a name, description, and text/plain mimetype

### Prompts

The server provides a single prompt:

- summarize-notes: Creates summaries of all stored notes
  - Optional "style" argument to control detail level (brief/detailed)
  - Generates prompt combining all current notes with style preference

### Tools

The server implements one tool:

- add-note: Adds a new note to the server
  - Takes "name" and "content" as required string arguments
  - Updates server state and notifies clients of resource changes

## Quickstart

### Install

1. Clone the repository:

```bash
git clone https://gitlab.rapidinnovation.tech/mcp-server/content-extractor-mcp/-/tree/dev?ref_type=heads
cd content-extractor-mcp
```

2. Create a virtual environment:

```bash
python -m venv .venv
source .venv/bin/activate  # On Windows use `.venv\Scripts\activate`
```

3. Create a `.env` file using the `.env.example` file:

```bash
cp .env.example .env
```

4. Run the server using `uv` command:

```bash
uv run content-extractor-mcp
```

This process will install all dependencies.

## Development

### Building and Publishing

To prepare the package for distribution:

1. Sync dependencies and update lockfile:

```bash
uv sync
```

2. Build package distributions:

```bash
uv build
```

This will create source and wheel distributions in the `dist/` directory.

3. Publish to PyPI:

```bash
uv publish
```

Note: You'll need to set PyPI credentials via environment variables or command flags:

- Token: `--token` or `UV_PUBLISH_TOKEN`
- Or username/password: `--username`/`UV_PUBLISH_USERNAME` and `--password`/`UV_PUBLISH_PASSWORD`

### Debugging

You can use the MCP inspector to debug the server. For uvx installations:

```bash
npx @modelcontextprotocol/inspector uvx content-extractor-mcp
```

Or if you've installed the package in a specific directory or are developing on it:

```bash
cd /Users/<USER>/Desktop/Projects/mcp-server/content-extractor-mcp
npx @modelcontextprotocol/inspector uv run content-extractor-mcp
```

Running `tail -n 20 -f ~/Library/Logs/Claude/mcp*.log` will show the logs from the server and may help you debug any issues.

### Using Docker

#### Build

Docker build:

```bash
docker build -t content-extractor-mcp .
```

#### Run

Docker run:

```bash
docker run -it content-extractor-mcp

docker run -it --env-file .env content-extractor-mcp
```
