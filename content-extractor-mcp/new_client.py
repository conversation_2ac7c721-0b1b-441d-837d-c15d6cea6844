import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


async def main():
    # Set server parameters
    server_params = StdioServerParameters(
        command="content-extractor-mcp",  # Path to server.py
        args=[
            "C://Users//INDIA//Desktop//mcp//content-extractor-mcp-MCP-server//content_extractor_mcp//src//content_extractor_mcp//server.py",
            "content-extractor-mcp",
            "run",
            "content-extractor-mcp",
        ],  # Command line arguments (if needed)
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize session
            await session.initialize()

            try:
                # Get list of available tools
                tools = await session.list_tools()

                print(f"Available tools: {tools}")

                # Example of calling a tool
                tool_result = await session.call_tool(
                    "WebCrawler",
                    arguments={
                        "link": "https://goyalpramod.github.io/blogs/demysitifying_diffusion_models/?s=08",
                    },
                )

                print(f"Tool execution result: {tool_result}")

            except Exception as e:
                print(f"An error occurred: {e}")


if __name__ == "__main__":
    print("Running")
    asyncio.run(main())
# https://goyalpramod.github.io/blogs/demysitifying_diffusion_models/?s=08
