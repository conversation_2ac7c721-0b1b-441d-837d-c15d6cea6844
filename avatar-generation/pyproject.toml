[project]
name = "avatar-video-generation"
version = "0.1.0"
description = "A MCP server project"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
 "g4f>=*******",
 "google-generativeai>=0.8.4",
 "mcp>=1.5.0",
 "openai>=1.68.2",
 "termcolor>=2.5.0",
 "werkzeug>=3.1.3",
]
[[project.authors]]
name = "AkashAaglawe22012"
email = "<EMAIL>"

[build-system]
requires = [ "hatchling",]
build-backend = "hatchling.build"

[project.scripts]
avatar-video-generation = "avatar_video_generation:main"
