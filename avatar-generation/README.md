# avtar-video-generation

A MCP server project

# Overview

The Avatar Generation MCP server utilizes Tavus to generate AI-powered avatar videos. It takes an avatar ID and a script as input, creating a personalized video with realistic speech and expressions. The server processes the input to synthesize lifelike avatars that match the given script. Once the video is generated, it provides an S3 URL as output, allowing seamless access, storage, and sharing. This solution is ideal for businesses, content creators, and automation workflows, enabling scalable and dynamic video generation. With its efficient processing and integration capabilities, the Avatar Generation MCP server simplifies avatar-based video content creation.

Let me know if you’d like any refinements!

# Quickstart

# At client.py file

"
server_params = StdioServerParameters(
command="python",
args=['used your mcp server path],
env=None
)

"

# Installation

# 1. Clone the Repository:

## Installation

1. Clone the Repository:

```bash
git clone https://gitlab.rapidinnovation.tech/mcp-server/avatar-generation-mcp.git
cd script-generation-mcp
```

2. Create a Virtual Environment (Recommended):

```bash
python3 -m venv .venv
source .venv/bin/activate  # Linux/macOS
.venv\Scripts\activate  # Windows
```

```bash
3. Run the Server:
uv run client.py (mcp server path)
```

# Using uv (recommended)

When using uv no specific installation is needed. We will use uvx to directly run avatar-clone.

# Using PIP

Alternatively, you can install audio via pip:

```bash
pip install script_generator_server
```

After installation, you can run it as a script using:

```bash
python -m script_generator_server
```

# Configuration

#Usage with Claude Desktop
On MacOS:` ~/Library/Application\ Support/Claude/claude_desktop_config.json` On Windows:` %APPDATA%/Claude/claude_desktop_config.json`

Development/Unpublished Servers Configuration

```bash
"mcpServers": {
  "avatar-video-generation": {
    "command": "uv",
    "args": [
      "--directory",
      "/Users/<USER>/Desktop/Projects/ciny/ciny-main/mcp-server/avatar-video-generation",
      "run",
      "avatar-video-generation"
    ]
  }
}
```

Published Servers Configuration

```bash
"mcpServers": {
  "avatar-video-generation": {
    "command": "uvx",
    "args": [
      "avatar-video-generation"
    ]
  }
}
```

# Development

Building and Publishing
To prepare the package for distribution:

Sync dependencies and update lockfile:

```bash
uv sync
```

Build package distributions:

```bash
uv build
```

This will create source and wheel distributions in the dist/ directory.

Publish to PyPI:

```bash
uv publish
```

# Usage with Claude Desktop

Add this to your `claude_desktop_config.json`:
Using uvx

```bash
"mcpServers": {
  "avatar-video-generation": {
    "command": "uvx",
    "args": ["avatar-video-generation", "--repository", "path/to/git/repo"]
  }
}
```

Using docker

- Note: replace '/Users/<USER>' with the path that you want to be accessible by this tool

```bash
"mcpServers": {
  "avatar-video-generation": {
    "command": "docker",
    "args": ["run", "--rm", "-i", "--mount", "type=bind,src=/Users/<USER>/Users/<USER>", "avatar-video-generation"]
  }
}
```

Using pip installation

```bash
"mcpServers": {
  "avatar-video-generation": {
    "command": "python",
    "args": ["-m", "avatar-video-generation", "--repository", "path/to/git/repo"]
  }
}
```

# Docker

````bash
{
  "mcpServers": {
    "avatar-video-generation": {
</details>

<details>
  <summary>Using docker</summary>

- Note: replace '/Users/<USER>' with the path that you want to be accessible by this tool

```json
"mcpServers": {
  "avatar-video-generation": {
    "command": "docker",
    "args": ["run", "--rm", "-i", "--mount", "type=bind,src=/Users/<USER>/Users/<USER>", "avatar-video-generation"]
  }
}
````

</details>

<details>
  <summary>Using pip installation</summary>

```json
"mcpServers": {
  "avatar-video-generation": {
    "command": "python",
    "args": ["-m", "aavatar-video-generation", "--repository", "path/to/git/repo"]
  }
}
```

</details>

### Usage with [Zed](https://github.com/zed-industries/zed)

Add to your Zed settings.json:

<details>
  <summary>Using uvx</summary>

```json
"context_servers": [
  "avatar-video-generation": {
    "command": {
      "path": "uvx",
      "args": ["avatar-video-generation"]
    }
  }
],
```

</details>

<details>
  <summary>Using pip installation</summary>

```json
"context_servers": {
  "avatar-video-generation": {
    "command": {
      "path": "python",
      "args": ["-m", "avatar-video-generation"]
    }
  }
},
```

</details>

### Debugging

Since MCP servers run over stdio, debugging can be challenging. For the best debugging experience, we strongly recommend using the [MCP Inspector](https://github.com/modelcontextprotocol/inspector).

You can launch the MCP Inspector via [`npm`](https://docs.npmjs.com/downloading-and-installing-node-js-and-npm) with this command:

```bash
npx @modelcontextprotocol/inspector uv --directory /Users/<USER>/Desktop/Projects/ciny/ciny-main/mcp-server/avatar-generation run avatar-generation
```

Upon launching, the Inspector will display a URL that you can access in your browser to begin debugging.

### Docker

```json
{
  "mcpServers": {
    "avatar-video-generation": {
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "-i",
        "--mount",
        "type=bind,src=/Users/<USER>/Desktop,dst=/projects/Desktop",
        "--mount",
        "type=bind,src=/path/to/other/allowed/dir,dst=/projects/other/allowed/dir,ro",
        "--mount",
        "type=bind,src=/path/to/file.txt,dst=/projects/path/to/file.txt",
        "avatar-video-generation"
      ]
    }
  }
}
```

### UVX

```json
{
  "mcpServers": {
    "avatar-video-generation": {
      "command": "uv",
      "args": [
        "--directory",
        "/<path to mcp-servers>/avatar-video-generation",
        "run",
        "avatar-video-generation"
      ]
    }
  }
}
```

## Build

Docker build:

```bash
docker build -t avatar-video-generation .
```

## License

This MCP server is licensed under the MIT License. This means you are free to use, modify, and distribute the software, subject to the terms and conditions of the MIT License. For more details, please see the LICENSE file in the project repository.
