import re
import g4f
import json
import openai
import google.generativeai as genai
from g4f.client import Client
from termcolor import colored
from enum import Enum
from typing import Tuple, List
from .config import OPENAI_API_KEY
from ..constants.constant import TavusVoiceGenerationFailed
import requests
import time
from ..helper.config import TAVUS_API_KEY


class ModelProvider(Enum):
    OPENAI = "openai"


default_openai_model = "gpt-4o"


def generate_response(
    prompt: str,
    provider=ModelProvider.OPENAI,
) -> str:
    """
    Generate a script for a video, depending on the subject of the video.
    Args:
        video_subject (str): The subject of the video.
    Returns:
        str: The response from the AI model.
    """
    try:
        if provider == ModelProvider.OPENAI:
            openai.api_key = OPENAI_API_KEY
            response = openai.chat.completions.create(
                model=default_openai_model,
                messages=[{"role": "user", "content": prompt}],
            )
            token_used = response.usage.total_tokens
            print("openai token_used ===>>", token_used)
            response = response.choices[0].message.content
        # if provider == ModelProvider.GOOGLE:
        #     genai.configure(api_key=GEMINI_API_KEY)
        #     model = genai.GenerativeModel(default_gemini_model)
        #     response_model = model.generate_content(prompt)
        #     print(
        #         "google token_used ===>>",
        #         response_model.usage_metadata.total_token_count,
        #     )
        #     response = response_model.text
        # if provider == ModelProvider.DEEPSEEK:
        #     # for backward compatibility, you can still use `https://api.deepseek.com/v1` as `base_url`.
        #     client = OpenAI(api_key=DEEPSEEK_API, base_url="https://api.deepseek.com")
        #     response = client.chat.completions.create(
        #         model=default_deepseek_model,
        #         messages=[
        #             {"role": "system", "content": "You are a helpful assistant"},
        #             {"role": "user", "content": prompt},
        #         ],
        #         max_tokens=1024,
        #         temperature=0.7,
        #         stream=False,
        #     )
        #     response = response.choices[0].message.content
        # if provider == ModelProvider.ANTHROPIC:
        #     client = anthropic.Anthropic(
        #         api_key=CLAUDE_KEY,
        #     )
        #     message = client.messages.create(
        #         model=default_claude_model,
        #         max_tokens=1024,
        #         messages=[{"role": "user", "content": prompt}],
        #     )
        #     response = message.content
        return response
    except Exception as e:
        print(colored(f"[-] Error: openai generate_response {str(e)}", "red"))
        raise


def update_progress(task, generation_progress, percentage=5):
    add_percentage = int(generation_progress.split("/")[0]) // 20
    percentage = percentage + int(add_percentage)
    task.update_state(
        state="PROGRESS",
        meta={"progress": percentage, "status": "avatar generating ..."},
    )


headers = {"x-api-key": TAVUS_API_KEY, "Content-Type": "application/json"}


def get_video(video_id, task=None):
    url = f"https://tavusapi.com/v2/videos/{video_id}"
    # Maximum number of retries
    max_retries = 50
    retry_count = 0

    # Loop until we get the video_url or reach the maximum number of retries
    while retry_count < max_retries:

        response = requests.request("GET", url, headers=headers)

        response.raise_for_status()
        data = response.json()
        if data["status"] == "deleted":
            raise TavusVoiceGenerationFailed("Failed to avatar video generation")
            break
        if data["status"] == "ready":
            break
        else:
            generation_progress = data.get("generation_progress")
            if generation_progress and task:
                update_progress(task, generation_progress)

            # Code execution will pause for 5 minutes before continuing
            print("waiting for tavus video creation...")
            # If video_url is not obtained, wait for 30 seconds before retrying
            time.sleep(45)
            retry_count += 1

    return data


def get_videos(video_ids, task=None):
    response_data = []
    # l = [video_ids]
    for video_id in video_ids:
        res = get_video(video_id, task)
        response_data.append({"download_url": res["download_url"]})
    return response_data
