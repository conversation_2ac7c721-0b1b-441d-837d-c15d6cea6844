import os
from dotenv import load_dotenv

# Load environment variables from a .env file
load_dotenv()


# Access environment variables and store them in variables
DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")
HOST = os.getenv("HOST", "localhost")
PORT = int(os.getenv("PORT", 5004))

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

WEBHOOK_ENDPOINT = os.getenv("WEBHOOK_ENDPOINT")

HEYGEN_API_KEY = os.getenv("HEYGEN_API_KEY")

TAVUS_API_KEY = os.getenv("TAVUS_API_KEY")


# Default models for each provider
default_openai_model = "gpt-4o"
