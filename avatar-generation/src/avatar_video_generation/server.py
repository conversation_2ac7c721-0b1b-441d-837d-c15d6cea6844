from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
from pydantic import AnyUrl
import mcp.server.stdio

import uvicorn
import logging
from starlette.applications import Starlette
from starlette.routing import Route
import json
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware
from mcp.server.sse import SseServerTransport

from .services.clone import clone_avatar
from .services.video import generate_avatar_video, fetch_videos


from .constants.enum import Tools
from .constants.constant import Descriptions
from .constants.schema import CloneAvatar, GenerateAvatarVideo, FetchAvatarVideos


from .helper.config import HOST, PORT

# Store notes as a simple key-value dict to demonstrate state management
notes: dict[str, str] = {}

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

server = Server("avatar-video-generation")


@server.list_resources()
async def handle_list_resources() -> list[types.Resource]:
    """
    List available note resources.
    Each note is exposed as a resource with a custom note:// URI scheme.
    """
    return [
        types.Resource(
            uri=AnyUrl(f"note://internal/{name}"),
            name=f"Note: {name}",
            description=f"A simple note named {name}",
            mimeType="text/plain",
        )
        for name in notes
    ]


@server.read_resource()
async def handle_read_resource(uri: AnyUrl) -> str:
    """
    Read a specific note's content by its URI.
    The note name is extracted from the URI host component.
    """
    if uri.scheme != "note":
        raise ValueError(f"Unsupported URI scheme: {uri.scheme}")

    name = uri.path
    if name is not None:
        name = name.lstrip("/")
        return notes[name]
    raise ValueError(f"Note not found: {name}")


@server.list_prompts()
async def handle_list_prompts() -> list[types.Prompt]:
    """
    List available prompts.
    Each prompt can have optional arguments to customize its behavior.
    """
    return [
        types.Prompt(
            name="summarize-notes",
            description="Creates a summary of all notes",
            arguments=[
                types.PromptArgument(
                    name="style",
                    description="Style of the summary (brief/detailed)",
                    required=False,
                )
            ],
        )
    ]


@server.get_prompt()
async def handle_get_prompt(
    name: str, arguments: dict[str, str] | None
) -> types.GetPromptResult:
    """
    Generate a prompt by combining arguments with server state.
    The prompt includes all current notes and can be customized via arguments.
    """
    if name != "summarize-notes":
        raise ValueError(f"Unknown prompt: {name}")

    style = (arguments or {}).get("style", "brief")
    detail_prompt = " Give extensive details." if style == "detailed" else ""

    return types.GetPromptResult(
        description="Summarize the current notes",
        messages=[
            types.PromptMessage(
                role="user",
                content=types.TextContent(
                    type="text",
                    text=f"Here are the current notes to summarize:{detail_prompt}\n\n"
                    + "\n".join(
                        f"- {name}: {content}" for name, content in notes.items()
                    ),
                ),
            )
        ],
    )


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """
    List available tools.
    Each tool specifies its arguments using JSON Schema validation.
    """
    return [
        types.Tool(
            name=Tools.GENERATE_AVATAR_VIDEO,
            description=Descriptions.GENERATE_AVATAR_VIDEO,
            inputSchema=GenerateAvatarVideo.schema(),
        ),
        types.Tool(
            name=Tools.CLONE_AVATAR,
            description=Descriptions.CLONE_AVATAR,
            inputSchema=CloneAvatar.schema(),
        ),
        types.Tool(
            name=Tools.FETCH_AVATAR_VIDEOS,
            description=Descriptions.FETCH_AVATAR_VIDEOS,
            inputSchema=FetchAvatarVideos.schema(),
        ),
    ]


@server.call_tool()
async def handle_call_tool(
    name: str, arguments: dict | None
) -> list[types.TextContent | types.ImageContent | types.EmbeddedResource]:
    """
    Handle tool execution requests.
    Tools can modify server state and notify clients of changes.
    """

    try:

        match name:
            case Tools.CLONE_AVATAR:
                name = arguments.get("name")
                image_url = arguments.get("image_url")
                platform = arguments.get("platform")
                result = await clone_avatar(name, image_url, platform)

            case Tools.GENERATE_AVATAR_VIDEO:
                script = arguments.get("script")
                avatar_id = arguments.get("avatar_id")
                platform = arguments.get("platform")
                audio_urls = arguments.get("audio_urls")
                response = await generate_avatar_video(
                    platform,
                    script,
                    avatar_id,
                    audio_urls,
                )

                result = {"avatar_video_ids": response}

            case Tools.FETCH_AVATAR_VIDEOS:
                video_ids = arguments.get("avatar_video_ids")
                platform = arguments.get("platform")
                result = await fetch_videos(platform, video_ids)

            case _:
                return [types.TextContent(type="text", text=f"Unknown tool: {name}")]

    except Exception as error:
        print("Error:", error)
        error = {"message": f"{str(error)}", "is_error": True}
        return [types.TextContent(type="text", text=json.dumps(error, indent=2))]

    print("result: ", result)
    return [types.TextContent(type="text", text=json.dumps(result, indent=2))]


async def main():
    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="avatar-video-generation",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )


def create_app():

    sse = SseServerTransport("/avatar")

    class HandleSSE:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            async with self.sse.connect_sse(scope, receive, send) as streams:
                await server.run(
                    streams[0],
                    streams[1],
                    server.create_initialization_options(),
                )

    class HandleMessages:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            await self.sse.handle_post_message(scope, receive, send)

    routes = [
        Route("/sse", endpoint=HandleSSE(sse), methods=["GET"]),
        Route("/avatar", endpoint=HandleMessages(sse), methods=["POST"]),
    ]

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
        )
    ]

    return Starlette(routes=routes, middleware=middleware)


def start_server():
    app = create_app()
    logger.info(f"Starting server at {HOST}:{PORT}")
    uvicorn.run(app, host=HOST, port=PORT, timeout_keep_alive=120)


if __name__ == "__main__":
    while True:
        try:
            start_server()
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
            break
        except Exception as e:
            logger.error(f"Server crashed with error: {e}")
            continue
