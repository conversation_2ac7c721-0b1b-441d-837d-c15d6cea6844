from werkzeug.exceptions import InternalServerError
from ..constants.enum import PlatformEnum
from ..helper.llm_call import get_script_text
from ..providers.tavus import TavusService

from ..providers.heygen import HeygenService


async def generate_avatar_video(platform, script, avatar_id, audio_urls):

    if script:
        script = get_script_text(script)

    if platform == PlatformEnum.TAVUS:
        tavus_service = TavusService()

        if len(audio_urls) > 0:
            return await tavus_service.generate_videos(audio_urls, avatar_id)
        else:
            return await tavus_service.create_video_using_script(script, avatar_id)

    elif platform == PlatformEnum.HEYGEN:

        heygen_service = HeygenService()

        if len(audio_urls) > 0:
            return await heygen_service.create_video_using_audio(audio_urls, avatar_id)
        else:
            return await heygen_service.create_video_using_script(script, avatar_id)

    else:
        raise InternalServerError("Invalid platform")


async def fetch_videos(platform, video_ids):
    if platform == PlatformEnum.TAVUS:
        tavus_service = TavusService()
        avatar_video_urls = await tavus_service.get_videos(video_ids)
    elif platform == PlatformEnum.HEYGEN:
        heygen_service = HeygenService()
        avatar_video_urls = await heygen_service.get_videos(video_ids)
    else:
        raise InternalServerError("Invalid platform")

    return {"avatar_video_urls": avatar_video_urls, "mime_type": "video/mp4"}
