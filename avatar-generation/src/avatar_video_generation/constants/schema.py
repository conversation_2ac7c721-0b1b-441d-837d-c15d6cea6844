from pydantic import BaseModel, HttpUrl, constr, Field, model_validator
from .enum import PlatformEnum
from typing import Optional


class CloneAvatar(BaseModel):
    name: constr(min_length=1, max_length=100) = Field(
        ..., description="Name is required"
    )
    image_url: HttpUrl = Field(..., description="Image URL is required")
    platform: PlatformEnum = Field(
        default=PlatformEnum.TAVUS, description="Platform is optional, default is TAVUS"
    )


class GenerateAvatarVideo(BaseModel):
    script: Optional[constr(min_length=1, max_length=1000)] = Field(
        None,
        description="Script is optional but required if audio_urls is not provided",
    )
    avatar_id: constr(min_length=1, max_length=100) = Field(
        ..., description="Avatar ID is required"
    )
    platform: PlatformEnum = Field(
        default=PlatformEnum.TAVUS, description="Platform is optional, default is TAVUS"
    )
    audio_urls: Optional[list] = Field(
        None,
        description="Audio URLs are optional but required if script is not provided",
    )

    @model_validator(mode="after")
    def validate_script_or_audio_urls(cls, values):
        script = values.script
        audio_urls = values.audio_urls
        if not script and not audio_urls:
            raise ValueError("Either script or audio_urls must be provided")
        return values


class FetchAvatarVideos(BaseModel):
    avatar_video_ids: list = Field(..., description="Video IDs are required")
    platform: PlatformEnum = Field(
        default=PlatformEnum.TAVUS, description="Platform is optional, default is TAVUS"
    )
