from werkzeug.exceptions import HTTPException


def build_script_text_prompt(script: str) -> str:
    """
    Generate a comprehensive prompt for extracting clean, verbatim text from a video script.

    This function creates a detailed prompt that guides text extraction by specifying
    precise rules for processing video script content, ensuring only the essential
    spoken text is retained.

    Args:
        script (str): The original video script text to be processed.

    Returns:
        str: A carefully crafted prompt with explicit instructions for text extraction.
    """
    prompt = f"""
    Extract Video Script Text
    Context:
    You are a precise text extraction assistant tasked with converting a raw video script
    into a clean, coherent text representation.

    Source Script:
    {script}

    Extraction Guidelines:
    -------------------
    1. Content Preservation:
    - Capture only spoken dialogue and direct quotes
    - Maintain original sequence and narrative flow
    - Preserve speaker's exact wording and punctuation

    2. Content Filtering:
    - Completely remove:
        * Scene descriptions
        * Stage directions
        * Formatting markers (**, [], XML/HTML tags)
        * Technical screenplay annotations

    3. Text Refinement:
    - Remove speaker labels (e.g., **Narrator:**, **Host:**)
    - Eliminate square bracket annotations
    - Strip unnecessary whitespaces
    - Retain natural speech patterns, including ellipses

    4. Output Specifications:
    - Generate a continuous, uninterrupted text stream
    - Ensure readability and coherence
    - No additional formatting or structural elements

    Desired Output:
    --------------
    A pure, unembellished text representation of the script's spoken content,
    reflecting the original narrative essence.

    Proceed with meticulous text extraction."""

    return prompt


class BaseExceptionClass(HTTPException):
    code: int
    description: str


class TavusVoiceGenerationFailed(BaseExceptionClass):
    code = 500
    description = "Failed to tavus generate video in playht."


class Descriptions:
    CLONE_AVATAR = "clone the avatar using the image url"
    GENERATE_AVATAR_VIDEO = "generate the avatar video using the script"
    FETCH_AVATAR_VIDEOS = "fetch the avatar videos using the video ids"
