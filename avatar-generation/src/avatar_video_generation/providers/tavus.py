import requests
import time

from ..constants.constant import TavusVoiceGenerationFailed
from werkzeug.exceptions import InternalServerError
from ..helper.config import TAVUS_API_KEY, WEBHOOK_ENDPOINT


class TavusService:

    def __init__(self):
        self.headers = {
            "x-api-key": TAVUS_API_KEY,
            "Content-Type": "application/json",
        }

    def update_progress(self, task, generation_progress, percentage=5):
        add_percentage = int(generation_progress.split("/")[0]) // 20
        percentage = percentage + int(add_percentage)
        task.update_state(
            state="PROGRESS",
            meta={"progress": percentage, "status": "avatar generating ..."},
        )

    def get_video(self, video_id, task=None):
        url = f"https://tavusapi.com/v2/videos/{video_id}"

        # Maximum number of retries
        max_retries = 50
        retry_count = 0

        # Loop until we get the video_url or reach the maximum number of retries
        while retry_count < max_retries:

            response = requests.request("GET", url, headers=self.headers)

            response.raise_for_status()
            data = response.json()

            if data["status"] == "deleted":
                raise TavusVoiceGenerationFailed("Failed to avatar video generation")
                break
            if data["status"] == "ready":
                break
            else:
                generation_progress = data.get("generation_progress")
                if generation_progress and task:
                    self.update_progress(task, generation_progress)

                # Code execution will pause for 5 minutes before continuing
                print("waiting for tavus video creation...")
                # If video_url is not obtained, wait for 30 seconds before retrying
                time.sleep(45)
                retry_count += 1

        return data

    async def get_videos(self, video_ids, task=None):
        response_data = []
        for video_id in video_ids:
            res = self.get_video(video_id, task)
            response_data.append(res["download_url"])
        return response_data

    def get_replicas(self):
        url = "https://tavusapi.com/v2/replicas"

        response = requests.request("GET", url, headers=self.headers)

        response.raise_for_status()

        return response.json()["data"]

    def create_replica(self, payload):
        try:
            # Prepare the request to Tavus API
            url = (
                "https://tavusapi.com/v2/replicas"  # Replace with actual Tavus API URL
            )
            payload["callback_url"] = WEBHOOK_ENDPOINT
            # Forward the request to Tavus API
            response = requests.post(url, json=payload, headers=self.headers)
            # Check for errors
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print("error", str(e))
            raise e

    def delete_replica(self, replica_id):
        try:

            url = "https://tavusapi.com/v2/replicas/{replica_id}"

            response = requests.request("DELETE", url, headers=self.headers)

            print(response.text)

        except Exception as e:
            print("error", str(e))
            raise e

    def get_replica(self, replica_id):
        try:

            url = f"https://tavusapi.com/v2/replicas/{replica_id}"

            response = requests.request("GET", url, headers=self.headers)

            print(response.text)

            # Check for errors
            response.raise_for_status()

            return response.json()

        except Exception as e:
            print("error", str(e))
            raise e

    def create_video_using_audio(self, audio_url, replica_id):
        try:

            url = "https://tavusapi.com/v2/videos"

            payload = {
                "replica_id": replica_id,
                "audio_url": audio_url,
                "callback_url": WEBHOOK_ENDPOINT,
                "fast": True,
            }

            response = requests.request("POST", url, json=payload, headers=self.headers)

            print(response.text)

            response.raise_for_status()

            return response.json()
        except Exception as e:
            print("Error create_video_using_audio", str(e))
            raise InternalServerError("Avatar video generation failed")

    def create_video_using_script(self, script, replica_id):
        try:

            url = "https://tavusapi.com/v2/videos"

            payload = {
                "replica_id": replica_id,
                "script": script,
                "callback_url": WEBHOOK_ENDPOINT,
                "fast": True,
            }

            response = requests.request("POST", url, json=payload, headers=self.headers)

            print(response.text)

            response.raise_for_status()

            return response.json()
        except Exception as e:
            print("Error create_video_using_script", str(e))
            raise InternalServerError("Avatar video generation failed")

    async def generate_videos(self, audio_urls, replica_id):
        response_data = []
        for audio_url in audio_urls:
            res = self.create_video_using_audio(audio_url, replica_id)
            response_data.append(res["video_id"])
        return list(response_data)
