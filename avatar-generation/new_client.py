import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


async def main():
    # Set server parameters
    server_params = StdioServerParameters(
        command="avatar-video-generation",  # Path to server.py
        args=[
            "C://Users//INDIA//Desktop//mcp//avtar_video_generation//src//avatar_video_generation//server.py",
            "avatar-video-generation",
            "run",
            "avatar-video-generation",
        ],  # Command line arguments (if needed)
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize session
            await session.initialize()

            try:
                # Get list of available tools
                tools = await session.list_tools()

                print(f"Available tools: {tools}")

                # Example of calling a tool
                tool_result = await session.call_tool(
                    "generate_video",
                    arguments={
                        "script": """AI Revolution in Banking & Finance!

                                    Banking and finance are evolving faster than ever! AI is leading this revolution, making financial services smarter, safer, and more efficient. Let’s dive in!

                                    Gone are the days of long wait times! AI-powered chatbots now provide 24/7 customer support, instantly resolving queries, processing transactions, and even offering financial advice.

                                    Investing just got smarter! AI analyzes vast amounts of financial data in real time, detecting patterns and predicting market trends, helping investors make informed decisions with confidence.

                                    Your money’s safety is a top priority! AI-driven security systems monitor transactions, detect fraud instantly, and prevent cyber threats before they cause damage.

                                    No more lengthy paperwork! AI automates loan approvals, assessing eligibility within seconds, making banking seamless and hassle-free.

                                    From fraud prevention to smart investments, AI is reshaping the financial world! Are you ready for the future?

                                    Like, share, and subscribe for more AI insights!""",
                        "avatar_id": "rb6f1c308f5c",
                    },
                )

                print(f"Tool execution result: {tool_result}")

            except Exception as e:
                print(f"An error occurred: {e}")


if __name__ == "__main__":
    print("Running")
    asyncio.run(main())
